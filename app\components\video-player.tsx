"use client";

import { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Pause } from "lucide-react";
import { useVideo } from "../contexts/VideoContext";

export function VideoPlayer() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const { videoURL, recordedVideoBlob } = useVideo();

  // Set the video source when videoURL is available
  useEffect(() => {
    if (videoRef.current && videoURL) {
      videoRef.current.src = videoURL;
    }
  }, [videoURL]);

  const togglePlay = async () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
      setIsPlaying(false);
    } else {
      try {
        await video.play();
        setIsPlaying(true);
      } catch (error) {
        console.error("Video play failed:", error);
      }
    }
  };

  return (
    <div className="relative w-full aspect-video bg-gray-100 rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        controls={false}
      />

      {/* Debug information overlay */}
      <div className="absolute top-4 right-4 bg-black/70 text-white text-xs p-2 rounded">
        <div>Video URL: {videoURL ? "✅" : "❌"}</div>
        <div>Video Blob: {recordedVideoBlob ? "✅" : "❌"}</div>
        <div>
          Blob Size:{" "}
          {recordedVideoBlob
            ? `${Math.round(recordedVideoBlob.size / 1024)}KB`
            : "N/A"}
        </div>
      </div>

      {/* Play/Pause overlay - only show if video is available */}
      {videoURL && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            onClick={togglePlay}
            size="lg"
            className="w-16 h-16 rounded-full bg-white/20 hover:bg-white/30 backdrop-blur-sm border-2 border-white/50"
          >
            {isPlaying ? (
              <Pause className="w-8 h-8 text-white" />
            ) : (
              <Play className="w-8 h-8 text-white ml-1" />
            )}
          </Button>
        </div>
      )}

      {/* Show message when no video is available */}
      {!videoURL && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-500 text-sm">
          No video recorded yet
        </div>
      )}

      {/* Progress bar */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="w-full h-1 bg-white/30 rounded-full">
          <div className="w-1/3 h-full bg-white rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
