import { Box, Container, Typography } from "@mui/material"
import { useTranslation } from "../hooks/useTranslation"
import CheckIcon from "@mui/icons-material/Check"

function Success() {
  const { t } = useTranslation()

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(180deg, #f5a742 0%, #e69429 100%)",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 3,
        color: "white",
      }}
    >
      <Container maxWidth="sm" sx={{ textAlign: "center" }}>
        {/* Logo */}
        <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", mb: 4 }}>
          <img
            src="/images/doright-logo.png"
            alt="Doright Logo"
            style={{
              height: "64px",
              width: "auto",
              filter: "brightness(0) invert(1)", // Make logo white
            }}
          />
        </Box>

        {/* Success Icon */}
        <Box sx={{ display: "flex", justifyContent: "center", mb: 4, position: "relative" }}>
          {/* Animated dots */}
          <Box
            className="recording-indicator"
            sx={{
              position: "absolute",
              top: -16,
              left: -16,
              width: 8,
              height: 8,
              bgcolor: "white",
              borderRadius: "50%",
              opacity: 0.6,
            }}
          />
          <Box
            className="recording-indicator"
            sx={{
              position: "absolute",
              top: -8,
              right: -24,
              width: 4,
              height: 4,
              bgcolor: "white",
              borderRadius: "50%",
              opacity: 0.4,
              animationDelay: "0.1s",
            }}
          />
          <Box
            className="recording-indicator"
            sx={{
              position: "absolute",
              bottom: -12,
              left: -24,
              width: 6,
              height: 6,
              bgcolor: "white",
              borderRadius: "50%",
              opacity: 0.5,
              animationDelay: "0.2s",
            }}
          />
          <Box
            className="recording-indicator"
            sx={{
              position: "absolute",
              bottom: -16,
              right: -8,
              width: 4,
              height: 4,
              bgcolor: "white",
              borderRadius: "50%",
              opacity: 0.3,
              animationDelay: "0.3s",
            }}
          />
          <Box
            className="recording-indicator"
            sx={{
              position: "absolute",
              top: 0,
              right: -32,
              width: 4,
              height: 4,
              bgcolor: "white",
              borderRadius: "50%",
              opacity: 0.4,
              animationDelay: "0.15s",
            }}
          />

          {/* Main check icon */}
          <Box
            sx={{
              width: 80,
              height: 80,
              bgcolor: "white",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <CheckIcon sx={{ fontSize: 40, color: "#f5a742" }} />
          </Box>
        </Box>

        {/* Main Message */}
        <Box sx={{ maxWidth: 400, mx: "auto" }}>
          <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
            {t("success.title", "Doright Verification Video Submitted — We're On It!")}
          </Typography>

          <Box sx={{ mt: 4, opacity: 0.9 }}>
            <Typography variant="h6" paragraph>
              {t(
                "success.message1",
                "We've got your submission and it's under review. Expect a confirmation soon once your NGO is verified.",
              )}
            </Typography>

            <Typography variant="h6">
              {t("success.message2", "Welcome to doright – where intention meets action. Together, we Act for Impact!")}
            </Typography>
          </Box>
        </Box>
      </Container>
    </Box>
  )
}

export default Success
