# Location Verification Feature

This document describes the location verification feature implemented for the doright verification system.

## Overview

The location verification feature ensures that users are within 100 meters of their registered NGO address before they can proceed with video verification. This is implemented through a dynamic route `/requirements/[uniqueId]` that:

1. Fetches KYC information using the unique ID
2. Retrieves NGO location data
3. Requests user's current location
4. Calculates distance using the Haversine formula
5. Allows or blocks progression based on proximity

## API Endpoints Used

### 1. KYC Information
```
GET http://localhost:4000/api/kyc-informations?uniqueId={uniqueId}
```

**Response:**
```json
[
  {
    "id": 11,
    "ngo_id": 84920,
    "status": "Rejected",
    "verification_link": null,
    "uniqueId": "df89541c-1128-4476-9d23-2378ed641f0a",
    "uploadToken": null,
    "albumId": null,
    "uploadResponse": null,
    "createdAt": "2025-06-21T14:37:18.000Z",
    "updatedAt": "2025-06-21T14:37:18.000Z"
  }
]
```

### 2. NGO Information
```
GET http://localhost:4000/api/ngos/{ngoId}
```

**Response:**
```json
{
  "id": 84920,
  "name": "Example NGO",
  "latitude": 19.0760,
  "longitude": 72.8777,
  "address": "123 NGO Street, Mumbai, Maharashtra"
}
```

## Usage

### Accessing the Location Verification Page

Navigate to: `/requirements/{uniqueId}`

Where `{uniqueId}` is the unique identifier from the KYC information.

Example: `/requirements/df89541c-1128-4476-9d23-2378ed641f0a`

### User Flow

1. **Loading State**: System fetches KYC and NGO data
2. **Permission Request**: User is asked to allow location access
3. **Location Verification**: System calculates distance between user and NGO
4. **Result**:
   - **Success**: User is within 100m, can proceed to requirements checklist
   - **Too Far**: User is beyond 100m, must visit NGO location
   - **Error**: Location access denied or other errors

## Technical Implementation

### Key Files

- `app/requirements/[uniqueId]/page.tsx` - Main component
- `app/utils/locationUtils.ts` - Utility functions
- `app/hooks/use-translation.tsx` - Updated with new translations

### Distance Calculation

Uses the Haversine formula to calculate the great-circle distance between two points on Earth:

```typescript
const haversineDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const toRad = (value: number) => (value * Math.PI) / 180;
  const R = 6371; // Earth's radius in kilometers
  
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  
  const a = Math.sin(dLat / 2) ** 2 + 
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2;
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  
  return R * c; // Distance in kilometers
};
```

### Location Verification Logic

```typescript
const proximityResult = verifyLocationProximity(
  userLocation,
  ngoLocation,
  0.1 // 100 meters in kilometers
);

if (proximityResult.isWithinRange) {
  // Allow user to proceed
} else {
  // Block user, show distance and NGO address
}
```

## Error Handling

The system handles various error scenarios:

1. **Invalid Unique ID**: No KYC information found
2. **Location Permission Denied**: User must allow location access
3. **Location Unavailable**: GPS/network issues
4. **API Errors**: Network or server issues
5. **Distance Too Far**: User must visit NGO location

## Translations

The feature supports multiple languages:
- English (en)
- Hindi (hi)
- Marathi (mr)

New translation keys added:
- `requirements.location_verification`
- `requirements.location_check`
- `requirements.location_permission`
- `requirements.allow_location`
- `requirements.location_verified`
- `requirements.location_success`
- `requirements.location_too_far`
- `requirements.distance_error`
- `requirements.current_distance`
- `requirements.required_distance`
- `requirements.retry_location`
- `requirements.error`
- `requirements.retry`

## Testing

Run the location utility tests:

```bash
npm test app/utils/__tests__/locationUtils.test.ts
```

## Security Considerations

1. **Location Privacy**: Location data is only used for verification, not stored
2. **API Security**: Ensure backend APIs validate requests properly
3. **Client-Side Validation**: Location verification is enforced on client-side but should be validated on server-side for security

## Browser Compatibility

The feature uses the Web Geolocation API which is supported in:
- Chrome 5+
- Firefox 3.5+
- Safari 5+
- Edge 12+
- Mobile browsers (iOS Safari, Chrome Mobile)

**Note**: HTTPS is required for geolocation in most modern browsers.
