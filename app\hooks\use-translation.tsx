"use client";

import { createContext, useContext, useState, type ReactNode } from "react";

type Language = "en" | "hi" | "mr";

interface TranslationContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, fallback: string) => string;
}

const TranslationContext = createContext<TranslationContextType | undefined>(
  undefined
);

const translations = {
  en: {
    "requirements.title": "Get Set to doright — Here's What You Need:",
    "requirements.subtitle": "Make sure you have:",
    "requirements.internet": "A stable internet connection",
    "requirements.helper": "Someone to hold the phone for you while recording",
    "requirements.certificate": "Your NGO's registration certificate",
    "requirements.access": "Access to your NGO office or operational site",
    "requirements.representative":
      "An authorized representative (with their name, designation, and mobile number and their government ID)",
    "requirements.important": "Important:",
    "requirements.location":
      "You must be within 100 meters of your registered NGO address.",
    "requirements.duration": "Keep your video under 2–3 minutes.",
    "requirements.continue": "Let's Do This",
    "requirements.agreement":
      "By continuing, you agree to be video verified — safely stored, just for doright.",
    "requirements.location_verification": "Location Verification Required",
    "requirements.location_check": "Location Check Required",
    "requirements.location_permission":
      "We need to verify that you are within 100 meters of your registered NGO address. Please allow location access to continue.",
    "requirements.allow_location": "Allow Location Access",
    "requirements.location_verified": "Location Verified ✓",
    "requirements.location_success":
      "Great! You are within the required distance from your NGO office.",
    "requirements.location_too_far": "Location Verification Failed",
    "requirements.distance_error": "You are too far from the NGO office",
    "requirements.current_distance": "Current distance:",
    "requirements.required_distance":
      "You must be within 100 meters of your registered NGO address to proceed with verification.",
    "requirements.retry_location": "Check Location Again",
    "requirements.error": "Verification Error",
    "requirements.retry": "Try Again",
  },
  hi: {
    "requirements.title":
      "doright के लिए तैयार हो जाइए — यहाँ है आपको क्या चाहिए:",
    "requirements.subtitle": "सुनिश्चित करें कि आपके पास है:",
    "requirements.internet": "एक स्थिर इंटरनेट कनेक्शन",
    "requirements.helper": "रिकॉर्डिंग के दौरान फोन पकड़ने के लिए कोई व्यक्ति",
    "requirements.certificate": "आपके NGO का पंजीकरण प्रमाणपत्र",
    "requirements.access": "आपके NGO कार्यालय या परिचालन स्थल तक पहुंच",
    "requirements.representative":
      "एक अधिकृत प्रतिनिधि (उनके नाम, पदनाम, और मोबाइल नंबर और उनकी सरकारी ID के साथ)",
    "requirements.important": "महत्वपूर्ण:",
    "requirements.location":
      "आपको अपने पंजीकृत NGO पते के 100 मीटर के भीतर होना चाहिए।",
    "requirements.duration": "अपना वीडियो 2-3 मिनट से कम रखें।",
    "requirements.continue": "चलिए करते हैं",
    "requirements.agreement":
      "जारी रखकर, आप वीडियो सत्यापन के लिए सहमत हैं — सुरक्षित रूप से संग्रहीत, केवल doright के लिए।",
    "requirements.location_verification": "स्थान सत्यापन आवश्यक",
    "requirements.location_check": "स्थान जांच आवश्यक",
    "requirements.location_permission":
      "हमें यह सत्यापित करना होगा कि आप अपने पंजीकृत NGO पते के 100 मीटर के भीतर हैं। कृपया जारी रखने के लिए स्थान पहुंच की अनुमति दें।",
    "requirements.allow_location": "स्थान पहुंच की अनुमति दें",
    "requirements.location_verified": "स्थान सत्यापित ✓",
    "requirements.location_success":
      "बहुत बढ़िया! आप अपने NGO कार्यालय से आवश्यक दूरी के भीतर हैं।",
    "requirements.location_too_far": "स्थान सत्यापन असफल",
    "requirements.distance_error": "आप NGO कार्यालय से बहुत दूर हैं",
    "requirements.current_distance": "वर्तमान दूरी:",
    "requirements.required_distance":
      "सत्यापन के साथ आगे बढ़ने के लिए आपको अपने पंजीकृत NGO पते के 100 मीटर के भीतर होना चाहिए।",
    "requirements.retry_location": "स्थान फिर से जांचें",
    "requirements.error": "सत्यापन त्रुटि",
    "requirements.retry": "फिर से कोशिश करें",
  },
  mr: {
    "requirements.title":
      "doright साठी तयार व्हा — तुम्हाला काय हवे ते येथे आहे:",
    "requirements.subtitle": "खात्री करा की तुमच्याकडे आहे:",
    "requirements.internet": "एक स्थिर इंटरनेट कनेक्शन",
    "requirements.helper": "रेकॉर्डिंग दरम्यान फोन धरण्यासाठी कोणीतरी",
    "requirements.certificate": "तुमच्या NGO चे नोंदणी प्रमाणपत्र",
    "requirements.access": "तुमच्या NGO कार्यालय किंवा कार्यक्षेत्रात प्रवेश",
    "requirements.representative":
      "एक अधिकृत प्रतिनिधी (त्यांचे नाव, पदनाम, आणि मोबाइल नंबर आणि त्यांचा सरकारी ID)",
    "requirements.important": "महत्वाचे:",
    "requirements.location":
      "तुम्ही तुमच्या नोंदणीकृत NGO पत्त्याच्या 100 मीटरच्या आत असले पाहिजे.",
    "requirements.duration": "तुमचा व्हिडिओ 2-3 मिनिटांपेक्षा कमी ठेवा.",
    "requirements.continue": "चला हे करूया",
    "requirements.agreement":
      "पुढे जाऊन, तुम्ही व्हिडिओ सत्यापनासाठी सहमत आहात — सुरक्षितपणे संग्रहित, फक्त doright साठी.",
    "requirements.location_verification": "स्थान सत्यापन आवश्यक",
    "requirements.location_check": "स्थान तपासणी आवश्यक",
    "requirements.location_permission":
      "आम्हाला हे सत्यापित करणे आवश्यक आहे की तुम्ही तुमच्या नोंदणीकृत NGO पत्त्याच्या 100 मीटरच्या आत आहात. कृपया पुढे जाण्यासाठी स्थान प्रवेशाची परवानगी द्या.",
    "requirements.allow_location": "स्थान प्रवेशाची परवानगी द्या",
    "requirements.location_verified": "स्थान सत्यापित ✓",
    "requirements.location_success":
      "उत्तम! तुम्ही तुमच्या NGO कार्यालयापासून आवश्यक अंतरात आहात.",
    "requirements.location_too_far": "स्थान सत्यापन अयशस्वी",
    "requirements.distance_error": "तुम्ही NGO कार्यालयापासून खूप दूर आहात",
    "requirements.current_distance": "सध्याचे अंतर:",
    "requirements.required_distance":
      "सत्यापनासह पुढे जाण्यासाठी तुम्ही तुमच्या नोंदणीकृत NGO पत्त्याच्या 100 मीटरच्या आत असले पाहिजे.",
    "requirements.retry_location": "स्थान पुन्हा तपासा",
    "requirements.error": "सत्यापन त्रुटी",
    "requirements.retry": "पुन्हा प्रयत्न करा",
  },
};

export function TranslationProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>("en");

  const t = (key: string, fallback: string): string => {
    return (translations[language] as any)?.[key] || fallback;
  };

  return (
    <TranslationContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </TranslationContext.Provider>
  );
}

export function useTranslation() {
  const context = useContext(TranslationContext);
  if (context === undefined) {
    throw new Error("useTranslation must be used within a TranslationProvider");
  }
  return context;
}
