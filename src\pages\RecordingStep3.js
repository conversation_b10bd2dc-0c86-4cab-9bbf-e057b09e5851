"use client";

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Button, Container, Typography } from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";
import DorightLogo from "../components/DorightLogo";
import VideoRecorder from "../components/VideoRecorder";

function RecordingStep3() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isRecording, setIsRecording] = useState(true); // Start as true since we're continuing from previous step
  const [timeLeft, setTimeLeft] = useState(90); // 1.5 minutes
  const [autoAdvanceTimer, setAutoAdvanceTimer] = useState(null);

  useEffect(() => {
    if (isRecording && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [isRecording, timeLeft]);

  useEffect(() => {
    if (isRecording) {
      // Set a timer to automatically advance to the next step after 30 seconds
      const timer = setTimeout(() => {
        navigate("/recording/step-4");
      }, 30000);

      setAutoAdvanceTimer(timer);

      return () => {
        if (autoAdvanceTimer) clearTimeout(autoAdvanceTimer);
      };
    }
  }, [isRecording, navigate]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}.${secs.toString().padStart(2, "0")} mins left`;
  };

  const handleFinish = () => {
    // Don't stop recording, just navigate to next step
    navigate("/recording/step-4");
  };

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        py: 4,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 3,
          flex: 1,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <DorightLogo />
          <Typography variant="body2" color="text.secondary">
            Step 3 of 5
          </Typography>
        </Box>

        <Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            {t("step3.title", "Show Off Your Impact Tools.")}
          </Typography>

          <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
            {t(
              "step3.description",
              "3. Display any posters, banners, documents, or items that reflect your NGO's work and purpose. Let your mission speak visually."
            )}
          </Typography>

          <VideoRecorder
            isRecording={isRecording}
            onRecordingChange={setIsRecording}
          />

          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {isRecording ? "Recording...." : "Ready to record"}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {formatTime(timeLeft)}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          <Button
            onClick={handleFinish}
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            sx={{ py: 1.5, fontSize: "1.1rem" }}
          >
            {t("step3.finish", "Finish")}
          </Button>

          <Typography
            variant="body2"
            align="center"
            sx={{ mt: 2, color: "text.secondary" }}
          >
            {t(
              "step3.help",
              "Want to retry? Tap Finish now. Otherwise, wait 30 s to automatically move to next step."
            )}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default RecordingStep3;
