"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useTranslation } from "../hooks/use-translation"
import { DorightLogo } from "../components/doright-logo"

export default function Requirements() {
  const router = useRouter()
  const { t } = useTranslation()

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
        <DorightLogo />

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
            {t("requirements.title", "Get Set to doright — Here's What You Need:")}
          </h1>

          <div className="space-y-4">
            <div>
              <h2 className="text-lg font-medium text-gray-800 mb-3">
                {t("requirements.subtitle", "Make sure you have:")}
              </h2>

              <div className="space-y-3 text-gray-600">
                <div className="flex items-start space-x-3">
                  <span className="font-medium">1.</span>
                  <span>{t("requirements.internet", "A stable internet connection")}</span>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="font-medium">2.</span>
                  <span>{t("requirements.helper", "Someone to hold the phone for you while recording")}</span>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="font-medium">3.</span>
                  <span>{t("requirements.certificate", "Your NGO's registration certificate")}</span>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="font-medium">4.</span>
                  <span>{t("requirements.access", "Access to your NGO office or operational site")}</span>
                </div>

                <div className="flex items-start space-x-3">
                  <span className="font-medium">5.</span>
                  <span>
                    {t(
                      "requirements.representative",
                      "An authorized representative (with their name, designation, and mobile number and their government ID)",
                    )}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <h3 className="font-semibold text-gray-800">{t("requirements.important", "Important:")}</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• {t("requirements.location", "You must be within 100 meters of your registered NGO address.")}</li>
                <li>• {t("requirements.duration", "Keep your video under 2–3 minutes.")}</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-6">
          <Button
            onClick={() => router.push("/ngo-confirmation")}
            className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
          >
            {t("requirements.continue", "Let's Do This")}
          </Button>

          <p className="text-sm text-gray-600 text-center">
            {t(
              "requirements.agreement",
              "By continuing, you agree to be video verified — safely stored, just for doright.",
            )}
          </p>
        </div>
      </div>
    </div>
  )
}
