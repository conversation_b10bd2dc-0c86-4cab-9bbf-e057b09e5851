"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useParams, useRouter } from "next/navigation";
import { useTranslation } from "../../hooks/use-translation";
import { DorightLogo } from "../../components/doright-logo";

export default function Instructions() {
  const router = useRouter();
  const { t } = useTranslation();
  const params = useParams();
  const uniqueId = params!.uniqueId as string;

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
        <DorightLogo />

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
            {t("instructions.title", "Capture These to Act for Impact.")}
          </h1>

          <div className="space-y-4">
            <p className="text-lg text-gray-700">
              {t(
                "instructions.subtitle",
                "Please record these key elements clearly:"
              )}
            </p>

            <div className="space-y-4 text-gray-600">
              <div className="flex items-start space-x-3">
                <span className="font-medium">1.</span>
                <div>
                  <span className="font-medium">
                    {t("instructions.entrance", "Entrance & Signage:")}
                  </span>
                  <span className="ml-1">
                    {t(
                      "instructions.entranceDesc",
                      "Show your nameplate and move slowly to capture surroundings."
                    )}
                  </span>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <span className="font-medium">2.</span>
                <div>
                  <span className="font-medium">
                    {t("instructions.workspace", "Workspace:")}
                  </span>
                  <span className="ml-1">
                    {t(
                      "instructions.workspaceDesc",
                      "Film your office, meeting rooms, activity rooms or workstations."
                    )}
                  </span>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <span className="font-medium">3.</span>
                <div>
                  <span className="font-medium">
                    {t("instructions.branded", "Branded Items:")}
                  </span>
                  <span className="ml-1">
                    {t(
                      "instructions.brandedDesc",
                      "Display posters, banners or official documents."
                    )}
                  </span>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <span className="font-medium">4.</span>
                <div>
                  <span className="font-medium">
                    {t("instructions.certificate", "Certificate:")}
                  </span>
                  <span className="ml-1">
                    {t(
                      "instructions.certificateDesc",
                      "Hold up your registration certificate ensure the name is visible and tilt side-to-side."
                    )}
                  </span>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <span className="font-medium">5.</span>
                <div>
                  <span className="font-medium">
                    {t(
                      "instructions.representative",
                      "Representative & ID Check:"
                    )}
                  </span>
                  <span className="ml-1">
                    {t(
                      "instructions.representativeDesc",
                      "Authorized person holds their government ID next to their face, states their"
                    )}
                  </span>
                  <span className="font-medium">
                    {" "}
                    name, mobile, and designation,
                  </span>
                  <span className="ml-1">
                    {t(
                      "instructions.script",
                      'and says: "Hi, I\'m [Full Name], [Designation], mobile number [XXX]. I confirm, I represent [NGO Name] and am completing this verification."'
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="pt-6">
          <Button
            onClick={() => router.push(`/recording/${uniqueId}`)}
            className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
          >
            {t("instructions.start", "Go DoRight")}
          </Button>
        </div>
      </div>
    </div>
  );
}
