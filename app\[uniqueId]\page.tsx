"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useParams, useRouter } from "next/navigation";
import { useTranslation } from "../hooks/use-translation";

type Language = "en" | "hi" | "mr";

interface PageProps {
  params: {
    uuid: string;
  };
}

export default function LanguageSelection() {
  const params = useParams();
  const uniqueId = params!.uniqueId as string;

  const router = useRouter();
  const { setLanguage, t } = useTranslation();

  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    router.push(`/requirements/${uniqueId}`);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-sm mx-auto space-y-8">
        {/* Logo */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2">
            {/* <div className="w-8 h-8 bg-orange-400 rounded-full flex items-center justify-center"> */}
            <img
              src="/images/doright-logo.png"
              alt="Doright Logo"
              style={{ height: "100px", width: "200" }}
            />{" "}
            {/* </div> */}
            <span className="text-2xl font-semibold text-gray-800">
              doright
            </span>
          </div>
        </div>

        {/* Main Content */}
        <div className="text-center space-y-6">
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
              Ready to doright?
            </h1>
            <p className="text-xl text-gray-600">
              Start Your Video Verification.
            </p>
          </div>

          <div className="py-8">
            <p className="text-xl text-gray-600 mb-8">
              Your Language, Your Impact.
            </p>

            <div className="space-y-4">
              <Button
                onClick={() => handleLanguageSelect("en")}
                className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
              >
                ENGLISH
              </Button>

              <Button
                onClick={() => handleLanguageSelect("hi")}
                className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
              >
                हिंदी
              </Button>

              <Button
                onClick={() => handleLanguageSelect("mr")}
                className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
              >
                मराठी
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
