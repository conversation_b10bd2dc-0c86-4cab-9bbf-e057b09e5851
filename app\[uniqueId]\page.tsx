"use client";
import { But<PERSON> } from "@/components/ui/button";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslation } from "../hooks/use-translation";
import { useState, useEffect } from "react";
import { fetchKycInformation, KycInformation } from "../utils/locationUtils";

// Extended interface to match the new API response structure
interface KycInformationWithNgo extends KycInformation {
  ngoInfo?: {
    id: number;
    name: string;
    ngo_status: string;
    darpan_id: string;
  };
}

type Language = "en" | "hi" | "mr";

interface PageProps {
  params: {
    uuid: string;
  };
}

export default function LanguageSelection() {
  const params = useParams();
  const uniqueId = params!.uniqueId as string;

  const router = useRouter();
  const { setLanguage, t } = useTranslation();

  // State for status checking
  const [kycData, setKycData] = useState<KycInformationWithNgo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [statusError, setStatusError] = useState<string | null>(null);
  const [isAccessAllowed, setIsAccessAllowed] = useState(false);

  // Check KYC status when component mounts
  useEffect(() => {
    const checkKycStatus = async () => {
      try {
        setIsLoading(true);
        setStatusError(null);

        const kycResponse = await fetchKycInformation(uniqueId);
        if (!kycResponse || kycResponse.length === 0) {
          setStatusError("No verification record found for this link.");
          return;
        }

        const kycRecord = kycResponse[0] as KycInformationWithNgo;
        setKycData(kycRecord);

        // Check if status is "Pending" - only then allow access
        if (kycRecord.status === "Pending") {
          setIsAccessAllowed(true);
        } else {
          setIsAccessAllowed(false);
          // Set appropriate error message based on status
          if (
            kycRecord.status === "Approved" ||
            kycRecord.status === "Verified"
          ) {
            setStatusError(
              "Your verification has already been completed and approved."
            );
          } else if (kycRecord.status === "Rejected") {
            setStatusError(
              "Your verification has been reviewed. Please contact support for assistance."
            );
          } else {
            setStatusError("Your verification has already been processed.");
          }
        }
      } catch (error) {
        console.error("Error checking KYC status:", error);
        setStatusError("Unable to verify your access. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    if (uniqueId) {
      checkKycStatus();
    }
  }, [uniqueId]);

  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    router.push(`/requirements/${uniqueId}`);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-sm mx-auto space-y-8">
          {/* Logo */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center space-x-2">
              <img
                src="/images/doright-logo.png"
                alt="Doright Logo"
                style={{ height: "100px", width: "200" }}
              />
              <span className="text-2xl font-semibold text-gray-800">
                doright
              </span>
            </div>
          </div>

          {/* Loading Content */}
          <div className="text-center space-y-6">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-400 mx-auto"></div>
            <p className="text-lg text-gray-600">Verifying your access...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if access is not allowed
  if (!isAccessAllowed || statusError) {
    return (
      <div className="min-h-screen bg-white flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-sm mx-auto space-y-8">
          {/* Logo */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center space-x-2">
              <img
                src="/images/doright-logo.png"
                alt="Doright Logo"
                style={{ height: "100px", width: "200" }}
              />
              <span className="text-2xl font-semibold text-gray-800">
                doright
              </span>
            </div>
          </div>

          {/* Error Content */}
          <div className="text-center space-y-6">
            <div className="text-red-500 mb-4">
              <svg
                className="w-16 h-16 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>

            <div className="space-y-4">
              <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
                Access Not Available
              </h1>
              <p className="text-lg text-gray-600">{statusError}</p>
            </div>

            <div className="py-4">
              <p className="text-sm text-gray-600 text-center">
                {t(
                  "ngo.error",
                  "Spotted an error? Email <NAME_EMAIL> and we'll sort it out."
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show normal language selection if access is allowed
  return (
    <div className="min-h-screen bg-white flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-sm mx-auto space-y-8">
        {/* Logo */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2">
            <img
              src="/images/doright-logo.png"
              alt="Doright Logo"
              style={{ height: "100px", width: "200" }}
            />
            <span className="text-2xl font-semibold text-gray-800">
              doright
            </span>
          </div>
        </div>

        {/* Main Content */}
        <div className="text-center space-y-6">
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
              Ready to doright?
            </h1>
            <p className="text-xl text-gray-600">
              Start Your Video Verification.
            </p>
          </div>

          <div className="py-8">
            <p className="text-xl text-gray-600 mb-8">
              Your Language, Your Impact.
            </p>

            <div className="space-y-4">
              <Button
                onClick={() => handleLanguageSelect("en")}
                className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
              >
                ENGLISH
              </Button>

              <Button
                onClick={() => handleLanguageSelect("hi")}
                className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
              >
                हिंदी
              </Button>

              <Button
                onClick={() => handleLanguageSelect("mr")}
                className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
              >
                मराठी
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
