import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { TranslationProvider } from "./hooks/use-translation";
import { VideoProvider } from "./contexts/VideoContext";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Doright - Video Verification",
  description: "NGO Video Verification Platform",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <TranslationProvider>
          <VideoProvider>{children}</VideoProvider>
        </TranslationProvider>
      </body>
    </html>
  );
}
