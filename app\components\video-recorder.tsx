"use client";

import { useRef, useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Play, Pause } from "lucide-react";
import { useVideo } from "../contexts/VideoContext";

interface VideoRecorderProps {
  isRecording: boolean;
  onRecordingChange: (recording: boolean) => void;
}

export function VideoRecorder({
  isRecording,
  onRecordingChange,
}: VideoRecorderProps) {
  const webcamRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [facingMode, setFacingMode] = useState("environment");
  const [isInitializing, setIsInitializing] = useState(false);
  const { saveRecordedVideo } = useVideo();

  // Canvas drawing effect for recording overlay
  useEffect(() => {
    if (isRecording) {
      const interval = setInterval(() => {
        drawOnCanvas();
      }, 100);
      return () => clearInterval(interval);
    }
  }, [isRecording]);

  useEffect(() => {
    if (isRecording) {
      startRecording();
    } else if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      stopRecording();
    }
  }, [isRecording]);

  useEffect(() => {
    requestCameraPermission();
    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  const drawOnCanvas = () => {
    const video = webcamRef.current;
    const canvas = canvasRef.current;
    if (!video || !canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  };

  const requestCameraPermission = async () => {
    setIsInitializing(true);
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: facingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
        audio: true,
      });
      setStream(mediaStream);
      setHasPermission(true);

      if (webcamRef.current) {
        webcamRef.current.srcObject = mediaStream;
        webcamRef.current
          .play()
          .catch((err: any) => console.error("Error playing video:", err));
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
      setHasPermission(false);
    } finally {
      setIsInitializing(false);
    }
  };

  const startRecording = async () => {
    if (!stream) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    try {
      // Get canvas stream for video
      const videoStream = canvas.captureStream();

      // Get fresh audio stream
      const audioStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
      });

      // Combine video and audio streams
      if (audioStream.getAudioTracks().length > 0) {
        videoStream.addTrack(audioStream.getAudioTracks()[0]);
      }

      const mediaRecorder = new MediaRecorder(videoStream, {
        mimeType: "video/webm",
      });

      mediaRecorderRef.current = mediaRecorder;
      let chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: "video/webm" });
        saveRecordedVideo(blob);
        console.log("Recording size:", blob.size);
        chunks = []; // Reset chunks
      };

      mediaRecorder.start();
      console.log("Recording started");
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();
      console.log("Recording stopped");

      // Stop audio tracks
      if (stream) {
        stream.getAudioTracks().forEach((track) => track.stop());
      }
    }
  };

  if (!hasPermission) {
    return (
      <div className="w-full aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-gray-600">
            {isInitializing
              ? "Initializing camera..."
              : "Camera permission required"}
          </p>
          <Button
            onClick={requestCameraPermission}
            disabled={isInitializing}
            className="bg-orange-400 hover:bg-orange-500"
          >
            {isInitializing ? "Loading..." : "Allow Camera Access"}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden">
      {/* Hidden canvas for recording */}
      <canvas ref={canvasRef} style={{ display: "none" }} />

      {/* Main video display */}
      <video
        ref={webcamRef}
        autoPlay
        playsInline
        muted
        className="w-full h-full object-cover"
      />

      {/* Recording indicator */}
      {isRecording && (
        <div className="absolute top-4 left-4 flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          <span className="text-white text-sm font-medium">REC</span>
        </div>
      )}

      {/* Control overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <Button
          onClick={() => onRecordingChange(!isRecording)}
          size="lg"
          className="w-16 h-16 rounded-full bg-white/20 hover:bg-white/30 backdrop-blur-sm border-2 border-white/50"
        >
          {isRecording ? (
            <Pause className="w-8 h-8 text-white" />
          ) : (
            <Play className="w-8 h-8 text-white ml-1" />
          )}
        </Button>
      </div>
    </div>
  );
}
