"use client";

import { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Pause } from "lucide-react";
import { useVideo } from "../contexts/VideoContext";
import Webcam from "react-webcam";

interface VideoRecorderProps {
  isRecording: boolean;
  onRecordingChange: (recording: boolean) => void;
}

export function VideoRecorder({
  isRecording,
  onRecordingChange,
}: VideoRecorderProps) {
  const webcamRef = useRef<Webcam>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [facingMode, setFacingMode] = useState("environment");
  const { saveRecordedVideo } = useVideo();

  // Canvas drawing effect - runs when recording
  useEffect(() => {
    if (isRecording) {
      const interval = setInterval(() => {
        drawOnCanvas();
      }, 100);
      return () => clearInterval(interval);
    }
  }, [isRecording]);

  useEffect(() => {
    if (isRecording) {
      startRecording();
    } else if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      stopRecording();
    }
  }, [isRecording]);

  const drawOnCanvas = () => {
    const webcam = webcamRef.current;
    const canvas = canvasRef.current;
    if (!webcam || !canvas) return;

    // Get the video element from react-webcam
    const video = webcam.video;
    if (!video) return;

    // Check if video has valid dimensions
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      return;
    }

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    try {
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    } catch (error) {
      // Silently handle errors
    }
  };

  const startRecording = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    try {
      // Get canvas stream for video
      const videoStream = canvas.captureStream();

      // Get fresh audio stream
      const audioStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
      });

      // Combine video and audio streams
      if (audioStream.getAudioTracks().length > 0) {
        videoStream.addTrack(audioStream.getAudioTracks()[0]);
      }

      const mediaRecorder = new MediaRecorder(videoStream, {
        mimeType: "video/webm",
      });

      mediaRecorderRef.current = mediaRecorder;
      let chunks: Blob[] = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: "video/webm" });
        saveRecordedVideo(blob);
        chunks = []; // Reset chunks
      };

      mediaRecorder.start();
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();

      // Stop audio tracks if webcam has stream
      if (webcamRef.current?.stream) {
        webcamRef.current.stream
          .getAudioTracks()
          .forEach((track) => track.stop());
      }
    }
  };

  return (
    <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden">
      {/* Hidden canvas for recording */}
      <canvas ref={canvasRef} style={{ display: "none" }} />

      {/* React Webcam component */}
      <Webcam
        ref={webcamRef}
        audio={true}
        muted={true}
        screenshotFormat="image/jpeg"
        videoConstraints={{ facingMode }}
        className="w-full h-full object-cover"
      />

      {/* Recording indicator */}
      {isRecording && (
        <div className="absolute top-4 left-4 flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          <span className="text-white text-sm font-medium">REC</span>
        </div>
      )}

      {/* Control overlay */}
      <div className="absolute inset-0 flex items-center justify-center">
        <Button
          onClick={() => onRecordingChange(!isRecording)}
          size="lg"
          className="w-16 h-16 rounded-full bg-white/20 hover:bg-white/30 backdrop-blur-sm border-2 border-white/50"
        >
          {isRecording ? (
            <Pause className="w-8 h-8 text-white" />
          ) : (
            <Play className="w-8 h-8 text-white ml-1" />
          )}
        </Button>
      </div>
    </div>
  );
}
