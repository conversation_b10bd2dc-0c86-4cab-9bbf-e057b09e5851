"use client"

import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { Box, Button, Container, Typography, Paper } from "@mui/material"
import { useTranslation } from "../hooks/useTranslation"
import <PERSON>right<PERSON>ogo from "../components/DorightLogo"
import VideoRecorder from "../components/VideoRecorder"

function RecordingStep5() {
  const navigate = useNavigate()
  const { t } = useTranslation()
  const [isRecording, setIsRecording] = useState(true) // Start as true since we're continuing from previous step
  const [timeLeft, setTimeLeft] = useState(30) // 30 seconds
  const [autoAdvanceTimer, setAutoAdvanceTimer] = useState(null)

  useEffect(() => {
    if (isRecording && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [isRecording, timeLeft])

  useEffect(() => {
    if (isRecording) {
      // Set a timer to automatically advance to the next step after 30 seconds
      const timer = setTimeout(() => {
        navigate("/review")
      }, 30000)

      setAutoAdvanceTimer(timer)

      return () => {
        if (autoAdvanceTimer) clearTimeout(autoAdvanceTimer)
      }
    }
  }, [isRecording, navigate])

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}.${secs.toString().padStart(2, "0")} mins left`
  }

  const handleFinish = () => {
    // Don't stop recording, just navigate to next step
    navigate("/review")
  }

  return (
    <Container maxWidth="sm" sx={{ minHeight: "100vh", display: "flex", flexDirection: "column", py: 4 }}>
      <Box sx={{ width: "100%", maxWidth: 360, mx: "auto", display: "flex", flexDirection: "column", gap: 3, flex: 1 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <DorightLogo />
          <Typography variant="body2" color="text.secondary">
            Step 5 of 5
          </Typography>
        </Box>

        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600, color: "text.primary" }}>
            {t("step5.title", "Let's wrap this up the right way!")}
          </Typography>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" fontWeight={500} sx={{ color: "text.secondary", mb: 1 }}>
              {t("step5.subtitle", "5. Your authorized representative holds their Government ID, says:")}
            </Typography>
            <Paper elevation={0} sx={{ bgcolor: "grey.50", p: 2, borderRadius: 2 }}>
              <Typography variant="body2">
                {t(
                  "step5.script",
                  '"Hi, I\'m [Full Name], [Designation], mobile number [XXX]. I confirm I represent [NGO Name] and am completing this verification."',
                )}
              </Typography>
            </Paper>
          </Box>

          <VideoRecorder isRecording={isRecording} onRecordingChange={setIsRecording} />

          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {isRecording ? "Recording...." : "Ready to record"}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {formatTime(timeLeft)}
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          <Button
            onClick={handleFinish}
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            sx={{ py: 1.5, fontSize: "1.1rem" }}
          >
            {t("step5.finish", "Finish")}
          </Button>

          <Typography variant="body2" align="center" sx={{ mt: 2, color: "text.secondary" }}>
            {t("step5.help", "Want to retry? Tap Finish now. Otherwise, wait 30 s to automatically move to next step.")}
          </Typography>
        </Box>
      </Box>
    </Container>
  )
}

export default RecordingStep5
