"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslation } from "../../hooks/use-translation";
import { DorightLogo } from "../../components/doright-logo";
import { useState, useEffect } from "react";
import {
  fetchKycInformation,
  fetchNGOData,
  KycInformation,
  NGOData,
} from "../../utils/locationUtils";

// Extended interface to match the new API response structure
interface KycInformationWithNgo extends KycInformation {
  ngoInfo?: {
    id: number;
    name: string;
    ngo_status: string;
    darpan_id: string;
    current_address: string;
  };
}

export default function NGOConfirmation() {
  const router = useRouter();
  const { t } = useTranslation();
  const params = useParams();
  const uniqueId = params!.uniqueId as string;

  // State for storing fetched data
  const [kycData, setKycData] = useState<KycInformationWithNgo | null>(null);
  const [ngoData, setNgoData] = useState<NGOData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch KYC information
        const kycResponse = await fetchKycInformation(uniqueId);
        if (!kycResponse || kycResponse.length === 0) {
          throw new Error("No KYC information found for this unique ID");
        }

        const kycRecord = kycResponse[0] as KycInformationWithNgo;
        console.log("kycRecord", kycRecord);
        setKycData(kycRecord);

        // Fetch NGO data only if ngoInfo is not included in KYC response
        if (!kycRecord.ngoInfo) {
          const ngoResponse = await fetchNGOData(kycRecord.ngo_id);
          setNgoData(ngoResponse);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };

    if (uniqueId) {
      fetchData();
    }
  }, [uniqueId]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white flex flex-col p-6">
        <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
          <DorightLogo />
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-400 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading NGO information...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-white flex flex-col p-6">
        <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
          <DorightLogo />
          <div className="text-center py-12">
            <div className="text-red-500 mb-4">
              <svg
                className="w-12 h-12 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              Error Loading Data
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-orange-400 hover:bg-orange-500 text-white"
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
        <DorightLogo />

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
            {t("ngo.title", "Confirm and Empower Your NGO.")}
          </h1>

          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-medium text-gray-700 mb-2">
                {t("ngo.name", "NGO Name:")}
              </h2>
              <p className="text-gray-800 font-medium">
                {kycData?.ngoInfo?.name || ngoData?.name || "Loading..."}
              </p>
            </div>

            <div>
              <h2 className="text-lg font-medium text-gray-700 mb-2">
                {t("ngo.darpan", "Darpan ID:")}
              </h2>
              <p className="text-gray-800 font-medium">
                {kycData?.ngoInfo?.darpan_id || "Loading..."}
              </p>
            </div>

            <div>
              <h2 className="text-lg font-medium text-gray-700 mb-2">
                {t("ngo.address", "NGO Address:")}
              </h2>
              <p className="text-gray-800 font-medium">
                {kycData?.ngoInfo?.current_address ||
                  ngoData?.current_address || "Loading..."}
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-6">
          <Button
            onClick={() => router.push(`/instructions/${uniqueId}`)}
            className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            disabled={!kycData || (!kycData.ngoInfo && !ngoData)}
          >
            {t("ngo.proceed", "Proceed")}
          </Button>

          <p className="text-sm text-gray-600 text-center">
            {t(
              "ngo.error",
              "Spotted an error? Email <NAME_EMAIL> and we'll sort it out."
            )}
          </p>
        </div>
      </div>
    </div>
  );
}
