"use client";

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  Container,
  Typography,
  LinearProgress,
} from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";
import DorightLogo from "../components/DorightLogo";
import VideoRecorder from "../components/VideoRecorder";

// Step configuration with auto-advance thresholds
const TOTAL_RECORDING_TIME = 150; // 2.5 minutes total
const STEPS = [
  {
    id: 1,
    title: "Let's Begin at the Front — Entrance & Signage",
    description:
      "1. Begin with your NGO's entrance. Make sure the nameplate or signage is clear, then gently pan the camera to show the area around.",
    autoAdvanceAt: 30, // Auto-advance after 30 seconds
  },
  {
    id: 2,
    title: "Show Where the Impact Happens.",
    description:
      "2. Give us a quick tour of your office, meeting rooms, or workstations — the spaces where your team makes change real.",
    autoAdvanceAt: 60, // Auto-advance after 1 minute total
  },
  {
    id: 3,
    title: "Show Off Your Impact Tools.",
    description:
      "3. Display any posters, banners, documents, or items that reflect your NGO's work and purpose. Let your mission speak visually.",
    autoAdvanceAt: 90, // Auto-advance after 1.5 minutes total
  },
  {
    id: 4,
    title: "Show Your Team in Action",
    description:
      "4. Introduce your team members or show them at work. If possible, briefly show any ongoing projects, beneficiaries being served, or activities that demonstrate your NGO's daily operations.",
    autoAdvanceAt: 120, // Auto-advance after 2 minutes total
  },
  {
    id: 5,
    title: "Let's wrap this up the right way!",
    description:
      '5. Your authorized representative holds their Government ID, says: "Hi, I\'m [Full Name], [Designation], mobile number [XXX]. I confirm I represent [NGO Name] and am completing this verification."',
    autoAdvanceAt: 150, // Auto-advance after 2.5 minutes total (end)
  },
];

function UnifiedRecording() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Main state
  const [currentStep, setCurrentStep] = useState(1);
  const [isRecording, setIsRecording] = useState(false);
  const [timeLeft, setTimeLeft] = useState(TOTAL_RECORDING_TIME);
  const [totalRecordingTime, setTotalRecordingTime] = useState(0);

  const currentStepData = STEPS[currentStep - 1];

  // Timer for countdown and auto-advance
  useEffect(() => {
    if (isRecording && timeLeft > 0) {
      const timer = setTimeout(() => {
        const newTimeLeft = timeLeft - 1;
        const newTotalTime = totalRecordingTime + 1;

        setTimeLeft(newTimeLeft);
        setTotalRecordingTime(newTotalTime);

        // Check if we should auto-advance to next step (only if we haven't manually advanced past this point)
        const currentStepData = STEPS[currentStep - 1];
        if (
          newTotalTime >= currentStepData.autoAdvanceAt &&
          currentStep < STEPS.length &&
          newTotalTime === currentStepData.autoAdvanceAt // Only auto-advance exactly at the threshold
        ) {
          setCurrentStep(currentStep + 1);
        } else if (newTimeLeft === 0) {
          // Recording complete, go to review
          setIsRecording(false);
          navigate("/review");
        }
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isRecording, timeLeft, totalRecordingTime, currentStep]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}.${secs.toString().padStart(2, "0")} mins left`;
  };

  const formatTotalTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleRecordingToggle = (recording) => {
    setIsRecording(recording);
  };

  const handleNextStep = () => {
    if (currentStep < STEPS.length) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);

      // When manually advancing, jump the timer to the CURRENT step's threshold (not next step's)
      const currentStepData = STEPS[currentStep - 1]; // Current step before advancing
      const timeToAdvanceTo =
        TOTAL_RECORDING_TIME - currentStepData.autoAdvanceAt;
      setTimeLeft(timeToAdvanceTo);
      setTotalRecordingTime(currentStepData.autoAdvanceAt);
    } else {
      // All steps completed, go to review
      setIsRecording(false);
      navigate("/review");
    }
  };

  const handleFinish = () => {
    if (currentStep < STEPS.length) {
      handleNextStep();
    } else {
      setIsRecording(false);
      navigate("/review");
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        py: 4,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 3,
          flex: 1,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <DorightLogo />
          <Typography variant="body2" color="text.secondary">
            Step {currentStep} of {STEPS.length}
          </Typography>
        </Box>

        {/* Progress Bar */}
        <Box sx={{ width: "100%" }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 6,
              borderRadius: 3,
              bgcolor: "grey.200",
              "& .MuiLinearProgress-bar": {
                bgcolor: "primary.main",
                borderRadius: 3,
              },
            }}
          />
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ mt: 1, display: "block" }}
          >
            Total Recording Time: {formatTotalTime(totalRecordingTime)}
          </Typography>
        </Box>

        {/* Content */}
        <Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            {t(`step${currentStep}.title`, currentStepData.title)}
          </Typography>

          <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
            {t(`step${currentStep}.description`, currentStepData.description)}
          </Typography>

          <VideoRecorder
            isRecording={isRecording}
            onRecordingChange={handleRecordingToggle}
          />

          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {isRecording ? "Recording..." : "Ready to record"}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {formatTime(timeLeft)}
            </Typography>
          </Box>
        </Box>

        {/* Controls */}
        <Box sx={{ mt: "auto", pt: 4 }}>
          {currentStep === 1 && !isRecording ? (
            <Button
              onClick={() => setIsRecording(true)}
              variant="contained"
              color="primary"
              fullWidth
              size="large"
              sx={{ py: 1.5, fontSize: "1.1rem" }}
            >
              {t("recording.start", "Start Recording")}
            </Button>
          ) : (
            <Button
              onClick={handleFinish}
              variant="contained"
              color="primary"
              fullWidth
              size="large"
              sx={{ py: 1.5, fontSize: "1.1rem" }}
            >
              {currentStep === STEPS.length
                ? t("recording.complete", "Complete Recording")
                : t("recording.next", "Next Step")}
            </Button>
          )}

          <Typography
            variant="body2"
            align="center"
            sx={{ mt: 2, color: "text.secondary" }}
          >
            {isRecording
              ? t(
                  "recording.autoAdvance",
                  "Recording will automatically advance to next step when timer reaches zero"
                )
              : currentStep === 1
              ? t(
                  "recording.help",
                  "Ready to go? Tap Start Recording to begin."
                )
              : t(
                  "recording.continue",
                  "Tap the play button on video to continue recording or Next Step to proceed."
                )}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default UnifiedRecording;
