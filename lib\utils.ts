import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Utility functions for location verification
 */

export interface KycInformation {
  id: number;
  ngo_id: number;
  status: string;
  verification_link: string | null;
  uniqueId: string;
  uploadToken: string | null;
  albumId: string | null;
  uploadResponse: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface NGOData {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  address: string;
  // Add other NGO fields as needed
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

/**
 * Calculate the distance between two coordinates using the Haversine formula
 * @param lat1 - Latitude of first point
 * @param lon1 - Longitude of first point
 * @param lat2 - Latitude of second point
 * @param lon2 - Longitude of second point
 * @returns Distance in kilometers
 */
export const haversineDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const toRad = (value: number) => (value * Math.PI) / 180;

  const R = 6371; // Earth's radius in kilometers

  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLon / 2) ** 2;

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in kilometers
};

/**
 * Fetch KYC information by unique ID
 * @param uniqueId - The unique identifier
 * @returns Promise with KYC information array
 */
export const fetchKycInformation = async (
  uniqueId: string
): Promise<KycInformation[]> => {
  const response = await fetch(
    `http://192.168.1.38:4000/api/kyc-informations?uniqueId=${uniqueId}`
  );

  if (!response.ok) {
    throw new Error(
      `Failed to fetch KYC information: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
};

/**
 * Fetch NGO data by NGO ID
 * @param ngoId - The NGO identifier
 * @returns Promise with NGO data
 */
export const fetchNGOData = async (ngoId: number): Promise<NGOData> => {
  const response = await fetch(`http://192.168.1.38:4000/api/ngos/${ngoId}`);

  if (!response.ok) {
    throw new Error(
      `Failed to fetch NGO data: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
};

/**
 * Get current user location using browser geolocation API
 * @returns Promise with user coordinates
 */
export const getCurrentLocation = (): Promise<LocationCoordinates> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error("Geolocation is not supported by this browser"));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        });
      },
      (error) => {
        let errorMessage = "Failed to get location";
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access denied by user";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out";
            break;
        }
        reject(new Error(errorMessage));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  });
};

/**
 * Check if user is within the required distance from NGO location
 * @param userLocation - User's current coordinates
 * @param ngoLocation - NGO's coordinates
 * @param maxDistanceKm - Maximum allowed distance in kilometers (default: 0.1 km = 100 meters)
 * @returns Object with verification result and distance
 */
export const verifyLocationProximity = (
  userLocation: LocationCoordinates,
  ngoLocation: LocationCoordinates,
  maxDistanceKm: number = 0.1
): { isWithinRange: boolean; distance: number; distanceInMeters: number } => {
  const distance = haversineDistance(
    userLocation.latitude,
    userLocation.longitude,
    ngoLocation.latitude,
    ngoLocation.longitude
  );

  const distanceInMeters = distance * 1000;

  return {
    isWithinRange: distance <= maxDistanceKm,
    distance,
    distanceInMeters,
  };
};
