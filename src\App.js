import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { TranslationProvider } from "./hooks/useTranslation";
import { VideoProvider } from "./contexts/VideoContext";

// Import pages
import LanguageSelection from "./pages/LanguageSelection";
import Requirements from "./pages/Requirements";
import RequirementsWithLocation from "./pages/RequirementsWithLocation";
import NGOConfirmation from "./pages/NGOConfirmation";
import Instructions from "./pages/Instructions";
import UnifiedRecording from "./pages/UnifiedRecording";
import Review from "./pages/Review";
import Success from "./pages/Success";

// Create a theme
const theme = createTheme({
  palette: {
    primary: {
      main: "#f5a742", // Orange color from the original design
    },
    secondary: {
      main: "#6b7280", // Gray color from the original design
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: "none",
          fontWeight: 500,
          padding: "10px 16px",
        },
        containedPrimary: {
          "&:hover": {
            backgroundColor: "#e69429",
          },
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TranslationProvider>
        <VideoProvider>
          <Router>
            <Routes>
              <Route path="/" element={<LanguageSelection />} />
              <Route path="/requirements" element={<Requirements />} />
              <Route
                path="/requirements/:uniqueId"
                element={<RequirementsWithLocation />}
              />
              <Route path="/ngo-confirmation" element={<NGOConfirmation />} />
              <Route path="/instructions" element={<Instructions />} />
              <Route path="/recording" element={<UnifiedRecording />} />
              <Route path="/review" element={<Review />} />
              <Route path="/success" element={<Success />} />
            </Routes>
          </Router>
        </VideoProvider>
      </TranslationProvider>
    </ThemeProvider>
  );
}

export default App;
