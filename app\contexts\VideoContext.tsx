"use client";

import { createContext, useContext, useState, ReactNode } from "react";

interface VideoContextType {
  recordedVideoBlob: Blob | null;
  videoURL: string | null;
  saveRecordedVideo: (blob: Blob) => void;
  clearRecordedVideo: () => void;
}

const VideoContext = createContext<VideoContextType | undefined>(undefined);

export function VideoProvider({ children }: { children: ReactNode }) {
  const [recordedVideoBlob, setRecordedVideoBlob] = useState<Blob | null>(null);
  const [videoURL, setVideoURL] = useState<string | null>(null);

  const saveRecordedVideo = (blob: Blob) => {
    setRecordedVideoBlob(blob);
    // Create a URL for the blob that can be used in video elements
    if (videoURL) {
      URL.revokeObjectURL(videoURL); // Clean up previous URL
    }
    const newVideoURL = URL.createObjectURL(blob);
    setVideoURL(newVideoURL);
  };

  const clearRecordedVideo = () => {
    if (videoURL) {
      URL.revokeObjectURL(videoURL);
    }
    setRecordedVideoBlob(null);
    setVideoURL(null);
  };

  const value: VideoContextType = {
    recordedVideoBlob,
    videoURL,
    saveRecordedVideo,
    clearRecordedVideo,
  };

  return (
    <VideoContext.Provider value={value}>
      {children}
    </VideoContext.Provider>
  );
}

export function useVideo(): VideoContextType {
  const context = useContext(VideoContext);
  if (context === undefined) {
    throw new Error("useVideo must be used within a VideoProvider");
  }
  return context;
}
