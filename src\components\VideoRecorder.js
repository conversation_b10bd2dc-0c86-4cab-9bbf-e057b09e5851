"use client";

import { useRef, useState, useEffect } from "react";
import { Box, Button, Typography } from "@mui/material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import { useVideo } from "../contexts/VideoContext";

function VideoRecorder({ isRecording, onRecordingChange }) {
  const videoRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const { saveRecordedVideo } = useVideo();

  useEffect(() => {
    if (isRecording) {
      startRecording();
    } else if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      stopRecording();
    }
  }, [isRecording]);

  useEffect(() => {
    requestCameraPermission();
    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  const requestCameraPermission = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment",
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
        audio: true,
      });
      setStream(mediaStream);
      setHasPermission(true);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.muted = true; // Mute the video to avoid feedback
        videoRef.current
          .play()
          .catch((err) => console.error("Error playing video:", err));
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
      setHasPermission(false);
    }
  };

  const startRecording = () => {
    if (!stream) return;

    try {
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "video/webm;codecs=vp9,opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setRecordedChunks((prev) => [...prev, event.data]);
        }
      };

      mediaRecorder.start(1000); // Collect data every second
      console.log("Recording started");
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();
      console.log("Recording stopped");

      // Create a blob from the recorded chunks
      const blob = new Blob(recordedChunks, { type: "video/webm" });

      // Save the video blob to context for use in Review page
      saveRecordedVideo(blob);
      console.log("Recording size:", blob.size);

      // Reset chunks for next recording
      setRecordedChunks([]);
    }
  };

  if (!hasPermission) {
    return (
      <Box
        sx={{
          width: "100%",
          aspectRatio: "16/9",
          bgcolor: "grey.100",
          borderRadius: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Box sx={{ textAlign: "center" }}>
          <Typography color="text.secondary" sx={{ mb: 2 }}>
            Camera permission required
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={requestCameraPermission}
          >
            Allow Camera Access
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        position: "relative",
        width: "100%",
        aspectRatio: "16/9",
        bgcolor: "black",
        borderRadius: 2,
        overflow: "hidden",
      }}
    >
      <video
        ref={videoRef}
        autoPlay
        playsInline
        style={{ width: "100%", height: "100%", objectFit: "cover" }}
      />

      {/* Recording indicator */}
      {isRecording && (
        <Box
          sx={{
            position: "absolute",
            top: 16,
            left: 16,
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          <Box
            className="recording-indicator"
            sx={{
              width: 12,
              height: 12,
              bgcolor: "error.main",
              borderRadius: "50%",
            }}
          />
          <Typography
            variant="caption"
            sx={{ color: "white", fontWeight: 500 }}
          >
            REC
          </Typography>
        </Box>
      )}

      {/* Control overlay */}
      <Box
        sx={{
          position: "absolute",
          inset: 0,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Button
          onClick={() => onRecordingChange(!isRecording)}
          sx={{
            width: 64,
            height: 64,
            borderRadius: "50%",
            bgcolor: "rgba(255, 255, 255, 0.2)",
            backdropFilter: "blur(4px)",
            border: "2px solid rgba(255, 255, 255, 0.5)",
            "&:hover": {
              bgcolor: "rgba(255, 255, 255, 0.3)",
            },
          }}
        >
          {isRecording ? (
            <PauseIcon sx={{ fontSize: 32, color: "white" }} />
          ) : (
            <PlayArrowIcon sx={{ fontSize: 32, color: "white", ml: 1 }} />
          )}
        </Button>
      </Box>
    </Box>
  );
}

export default VideoRecorder;
