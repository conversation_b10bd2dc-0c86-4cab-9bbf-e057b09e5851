"use client";
import { useNavigate } from "react-router-dom";
import { Box, Button, Container, Typography } from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";

function LanguageSelection() {
  const navigate = useNavigate();
  const { setLanguage } = useTranslation();

  const handleLanguageSelect = (lang) => {
    setLanguage(lang);
    navigate("/requirements");
  };

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        py: 6,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 4,
        }}
      >
        {/* Logo */}
        <Box sx={{ textAlign: "center", mb: 2 }}>
          <img
            src="/images/doright-logo.png"
            alt="Doright Logo"
            style={{ height: "48px", width: "auto" }}
          />
        </Box>

        {/* Main Content */}
        <Box sx={{ textAlign: "center", mb: 4 }}>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            Ready to doright?
          </Typography>
          <Typography variant="h5" sx={{ color: "text.secondary" }}>
            Start Your Video Verification.
          </Typography>
        </Box>

        <Box sx={{ py: 4 }}>
          <Typography variant="h6" sx={{ mb: 4, color: "text.secondary" }}>
            Your Language, Your Impact.
          </Typography>

          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <Button
              onClick={() => handleLanguageSelect("en")}
              variant="contained"
              color="primary"
              size="large"
              sx={{ py: 1.5, fontSize: "1.1rem" }}
            >
              ENGLISH
            </Button>

            <Button
              onClick={() => handleLanguageSelect("hi")}
              variant="contained"
              color="primary"
              size="large"
              sx={{ py: 1.5, fontSize: "1.1rem" }}
            >
              हिंदी
            </Button>

            <Button
              onClick={() => handleLanguageSelect("mr")}
              variant="contained"
              color="primary"
              size="large"
              sx={{ py: 1.5, fontSize: "1.1rem" }}
            >
              मराठी
            </Button>
          </Box>
        </Box>
      </Box>
    </Container>
  );
}

export default LanguageSelection;
