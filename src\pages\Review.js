"use client";

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Box, Button, Container, Typography, Grid } from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";
import DorightLogo from "../components/DorightLogo";
import VideoPlayer from "../components/VideoPlayer";
import { useVideo } from "../contexts/VideoContext";
import { uploadVideoToAPI, DEFAULT_DARPAN_ID } from "../utils/videoUpload";

function Review() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const { recordedVideoBlob, clearRecordedVideo } = useVideo();

  const handleSubmit = async () => {
    if (!recordedVideoBlob) {
      setSubmitError("No video recorded. Please record a video first.");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      console.log("Uploading video...", {
        size: recordedVideoBlob.size,
        type: recordedVideoBlob.type,
        darpanId: DEFAULT_DARPAN_ID,
      });

      const result = await uploadVideoToAPI(
        recordedVideoBlob,
        DEFAULT_DARPAN_ID
      );

      console.log("Upload result:", result);

      // Clear the video after successful upload
      clearRecordedVideo();
      navigate("/success");
    } catch (error) {
      console.error("Upload error:", error);
      setSubmitError(
        error.message || "Failed to upload video. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRetake = () => {
    clearRecordedVideo(); // Clear the recorded video before retaking
    navigate("/recording");
  };

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        py: 4,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 3,
          flex: 1,
        }}
      >
        <DorightLogo />

        <Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            {t("review.title", "Do It Right—Double Check!")}
          </Typography>

          <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
            {t(
              "review.description",
              "Check your video—ensure great lighting and that it meets all verification requirements before submitting."
            )}
          </Typography>

          <VideoPlayer />
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          {/* Error message */}
          {submitError && (
            <Box
              sx={{
                mb: 2,
                p: 2,
                bgcolor: "error.light",
                borderRadius: 1,
                border: "1px solid",
                borderColor: "error.main",
              }}
            >
              <Typography variant="body2" color="error.dark">
                {submitError}
              </Typography>
            </Box>
          )}

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Button
                onClick={handleRetake}
                variant="outlined"
                color="primary"
                fullWidth
                size="large"
                sx={{ py: 1.5, fontSize: "1.1rem" }}
                disabled={isSubmitting}
              >
                {t("review.retake", "Retake")}
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
                fullWidth
                size="large"
                sx={{ py: 1.5, fontSize: "1.1rem" }}
                disabled={isSubmitting || !recordedVideoBlob}
              >
                {isSubmitting
                  ? t("review.uploading", "Uploading...")
                  : t("review.submit", "Submit")}
              </Button>
            </Grid>
          </Grid>

          <Typography
            variant="body2"
            align="center"
            sx={{ mt: 2, color: "text.secondary" }}
          >
            {!recordedVideoBlob
              ? "Please record a video first"
              : t(
                  "review.help",
                  "Tap Play to check your KYC video and confirm it's all good."
                )}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default Review;
