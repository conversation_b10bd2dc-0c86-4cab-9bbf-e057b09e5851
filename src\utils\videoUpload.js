/**
 * Utility function to upload video to the backend API
 * @param {Blob} videoBlob - The recorded video blob
 * @param {string} darpanId - The darpan ID for the upload
 * @returns {Promise<Object>} - The API response
 */
export const uploadVideoToAPI = async (videoBlob, darpanId) => {
  if (!videoBlob) {
    throw new Error("No video blob provided");
  }

  if (!darpanId) {
    throw new Error("No darpan ID provided");
  }

  const formData = new FormData();

  // Create a file from the blob with proper naming
  const videoFile = new File([videoBlob], "recorded-video.webm", {
    type: "video/webm",
  });

  formData.append("file", videoFile);
  formData.append("darpanId", darpanId);

  console.log("Uploading video:", {
    fileName: videoFile.name,
    fileSize: videoFile.size,
    fileType: videoFile.type,
    darpanId: darpanId,
  });

  const response = await fetch(
    "http://localhost:4000/api/google-photos/upload",
    {
      method: "POST",
      body: formData,
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Upload failed: ${response.status} ${response.statusText}. ${errorText}`
    );
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error("Upload failed - server returned success: false");
  }

  return result;
};

/**
 * Default darpan ID for testing
 */
export const DEFAULT_DARPAN_ID = "77/888/99999999";
