"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "../../hooks/use-translation";
import { DorightLogo } from "../../components/doright-logo";
import {
  fetchKycInformation,
  fetchNGOData,
  getCurrentLocation,
  verifyLocationProximity,
  type KycInformation,
  type NGOData,
  type LocationCoordinates,
} from "../../utils/locationUtils";

interface LocationVerificationState {
  status:
    | "loading"
    | "requesting_permission"
    | "verifying"
    | "success"
    | "error"
    | "too_far";
  error?: string;
  kycData?: KycInformation;
  ngoData?: NGOData;
  userLocation?: LocationCoordinates;
  distance?: number;
  distanceInMeters?: number;
}

export default function RequirementsWithLocation() {
  const params = useParams();
  const router = useRouter();
  const { t } = useTranslation();
  const uniqueId = params!.uniqueId as string;

  const [verificationState, setVerificationState] =
    useState<LocationVerificationState>({
      status: "loading",
    });

  useEffect(() => {
    if (uniqueId) {
      initializeLocationVerification();
    }
  }, [uniqueId]);

  const initializeLocationVerification = async () => {
    try {
      setVerificationState({ status: "loading" });

      // Step 1: Fetch KYC information
      const kycData = await fetchKycInformation(uniqueId);

      if (!kycData || kycData.length === 0) {
        setVerificationState({
          status: "error",
          error: "No KYC information found for this unique ID",
        });
        return;
      }

      const kycRecord = kycData[0];

      // Step 2: Fetch NGO data
      const ngoData = await fetchNGOData(kycRecord.ngo_id);

      setVerificationState({
        status: "requesting_permission",
        kycData: kycRecord,
        ngoData,
      });
    } catch (error) {
      console.error("Error initializing location verification:", error);
      setVerificationState({
        status: "error",
        error:
          error instanceof Error
            ? error.message
            : "Failed to initialize verification",
      });
    }
  };

  const requestLocationPermission = async () => {
    try {
      setVerificationState((prev) => ({ ...prev, status: "verifying" }));

      // Get user's current location
      const userLocation = await getCurrentLocation();

      if (!verificationState.ngoData) {
        throw new Error("NGO data not available");
      }

      // Verify location proximity
      const proximityResult = verifyLocationProximity(userLocation, {
        latitude: verificationState.ngoData.latitude,
        longitude: verificationState.ngoData.longitude,
      });

      if (proximityResult.isWithinRange) {
        setVerificationState((prev) => ({
          ...prev,
          status: "success",
          userLocation,
          distance: proximityResult.distance,
          distanceInMeters: proximityResult.distanceInMeters,
        }));
      } else {
        setVerificationState((prev) => ({
          ...prev,
          status: "too_far",
          userLocation,
          distance: proximityResult.distance,
          distanceInMeters: proximityResult.distanceInMeters,
        }));
      }
    } catch (error) {
      console.error("Error getting location:", error);
      setVerificationState((prev) => ({
        ...prev,
        status: "error",
        error:
          error instanceof Error ? error.message : "Failed to get location",
      }));
    }
  };

  const handleContinue = () => {
    router.push(`/ngo-confirmation/${uniqueId}`);
  };

  const renderContent = () => {
    switch (verificationState.status) {
      case "loading":
        return (
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-400 mx-auto"></div>
            <p className="text-gray-600">Loading verification data...</p>
          </div>
        );

      case "requesting_permission":
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
                {t(
                  "requirements.location_verification",
                  "Location Verification Required"
                )}
              </h1>

              {verificationState.ngoData && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-2">
                    NGO Information:
                  </h3>
                  <p className="text-blue-700">
                    <strong>Name:</strong> {verificationState.ngoData.name}
                  </p>
                  <p className="text-blue-700">
                    <strong>Address:</strong>{" "}
                    {verificationState.ngoData.current_address}
                  </p>
                </div>
              )}

              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="font-semibold text-yellow-800 mb-2">
                  {t("requirements.location_check", "Location Check Required")}
                </h3>
                <p className="text-yellow-700 text-sm">
                  {t(
                    "requirements.location_permission",
                    "We need to verify that you are within 100 meters of your registered NGO address. Please allow location access to continue."
                  )}
                </p>
              </div>
            </div>

            <Button
              onClick={requestLocationPermission}
              className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            >
              {t("requirements.allow_location", "Allow Location Access")}
            </Button>
          </div>
        );

      case "verifying":
        return (
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-400 mx-auto"></div>
            <p className="text-gray-600">Verifying your location...</p>
          </div>
        );

      case "success":
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h1 className="text-2xl font-semibold text-green-800 leading-tight">
                {t("requirements.location_verified", "Location Verified ✓")}
              </h1>

              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-green-700">
                  {t(
                    "requirements.location_success",
                    `Great! You are within the required distance from your NGO office (${Math.round(
                      verificationState.distanceInMeters || 0
                    )} meters away).`
                  )}
                </p>
              </div>

              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-800">
                  {t(
                    "requirements.title",
                    "Get Set to doright — Here's What You Need:"
                  )}
                </h2>

                <div className="space-y-3 text-gray-600">
                  <div className="flex items-start space-x-3">
                    <span className="font-medium">1.</span>
                    <span>
                      {t(
                        "requirements.internet",
                        "A stable internet connection"
                      )}
                    </span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="font-medium">2.</span>
                    <span>
                      {t(
                        "requirements.helper",
                        "Someone to hold the phone for you while recording"
                      )}
                    </span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="font-medium">3.</span>
                    <span>
                      {t(
                        "requirements.certificate",
                        "Your NGO's registration certificate"
                      )}
                    </span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="font-medium">4.</span>
                    <span>
                      {t(
                        "requirements.access",
                        "Access to your NGO office or operational site"
                      )}
                    </span>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="font-medium">5.</span>
                    <span>
                      {t(
                        "requirements.representative",
                        "An authorized representative (with their name, designation, and mobile number and their government ID)"
                      )}
                    </span>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <h3 className="font-semibold text-gray-800">
                    {t("requirements.important", "Important:")}
                  </h3>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>
                      •{" "}
                      {t(
                        "requirements.location",
                        "You must be within 100 meters of your registered NGO address."
                      )}
                    </li>
                    <li>
                      •{" "}
                      {t(
                        "requirements.duration",
                        "Keep your video under 2–3 minutes."
                      )}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <Button
              onClick={handleContinue}
              className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            >
              {t("requirements.continue", "Let's Do This")}
            </Button>
          </div>
        );

      case "too_far":
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h1 className="text-2xl font-semibold text-red-800 leading-tight">
                {t(
                  "requirements.location_too_far",
                  "Location Verification Failed"
                )}
              </h1>

              <div className="bg-red-50 p-4 rounded-lg">
                <h3 className="font-semibold text-red-800 mb-2">
                  {t(
                    "requirements.distance_error",
                    "You are too far from the NGO office"
                  )}
                </h3>
                <p className="text-red-700 text-sm mb-2">
                  {t(
                    "requirements.current_distance",
                    `Current distance: ${Math.round(
                      verificationState.distanceInMeters || 0
                    )} meters`
                  )}
                </p>
                <p className="text-red-700 text-sm">
                  {t(
                    "requirements.required_distance",
                    "You must be within 100 meters of your registered NGO address to proceed with verification."
                  )}
                </p>
              </div>

              {verificationState.ngoData && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-2">
                    Please visit:
                  </h3>
                  <p className="text-blue-700">
                    <strong>{verificationState.ngoData.name}</strong>
                  </p>
                  <p className="text-blue-700">
                    {verificationState.ngoData.current_address}
                  </p>
                </div>
              )}
            </div>

            <Button
              onClick={requestLocationPermission}
              className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            >
              {t("requirements.retry_location", "Check Location Again")}
            </Button>
          </div>
        );

      case "error":
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h1 className="text-2xl font-semibold text-red-800 leading-tight">
                {t("requirements.error", "Verification Error")}
              </h1>

              <div className="bg-red-50 p-4 rounded-lg">
                <p className="text-red-700">{verificationState.error}</p>
              </div>
            </div>

            <Button
              onClick={initializeLocationVerification}
              className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            >
              {t("requirements.retry", "Try Again")}
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
        <DorightLogo />
        {renderContent()}
      </div>
    </div>
  );
}
