"use client"

import { useTranslation } from "../hooks/use-translation"
import { Check } from "lucide-react"

export default function Success() {
  const { t } = useTranslation()

  return (
    <div className="min-h-screen bg-gradient-to-b from-orange-400 to-orange-500 flex flex-col items-center justify-center p-6 text-white">
      <div className="w-full max-w-sm mx-auto text-center space-y-8">
        {/* Logo */}
        <div className="flex flex-col items-center justify-center mb-8">
          <img
            src="/images/doright-logo.png"
            alt="Doright Logo"
            className="h-16 w-auto invert" // Invert to make it white on orange background
          />
        </div>

        {/* Success Icon */}
        <div className="flex justify-center mb-8">
          <div className="relative">
            {/* Animated dots */}
            <div className="absolute -top-4 -left-4 w-2 h-2 bg-white rounded-full opacity-60 animate-pulse"></div>
            <div className="absolute -top-2 -right-6 w-1 h-1 bg-white rounded-full opacity-40 animate-pulse delay-100"></div>
            <div className="absolute -bottom-3 -left-6 w-1.5 h-1.5 bg-white rounded-full opacity-50 animate-pulse delay-200"></div>
            <div className="absolute -bottom-4 -right-2 w-1 h-1 bg-white rounded-full opacity-30 animate-pulse delay-300"></div>
            <div className="absolute top-0 -right-8 w-1 h-1 bg-white rounded-full opacity-40 animate-pulse delay-150"></div>

            {/* Main check icon */}
            <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center">
              <Check className="w-10 h-10 text-orange-400" strokeWidth={3} />
            </div>
          </div>
        </div>

        {/* Main Message */}
        <div className="space-y-4">
          <h1 className="text-3xl font-bold leading-tight">
            {t("success.title", "Doright Verification Video Submitted — We're On It!")}
          </h1>

          <div className="space-y-4 text-lg opacity-90">
            <p>
              {t(
                "success.message1",
                "We've got your submission and it's under review. Expect a confirmation soon once your NGO is verified.",
              )}
            </p>

            <p>
              {t("success.message2", "Welcome to doright – where intention meets action. Together, we Act for Impact!")}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
