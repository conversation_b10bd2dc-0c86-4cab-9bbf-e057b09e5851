"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslation } from "../../hooks/use-translation";
import { DorightLogo } from "../../components/doright-logo";
import { VideoPlayer } from "../../components/video-player";
import { useState, useEffect } from "react";
import { useVideo } from "../../contexts/VideoContext";
import { uploadVideoToAPI, DEFAULT_DARPAN_ID } from "../../utils/videoUpload";
import { fetchKycInformation, KycInformation } from "../../utils/locationUtils";

// Extended interface to match the new API response structure
interface KycInformationWithNgo extends KycInformation {
  ngoInfo?: {
    id: number;
    name: string;
    ngo_status: string;
    darpan_id: string;
  };
}

export default function Review() {
  const router = useRouter();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const { recordedVideoBlob, clearRecordedVideo } = useVideo();

  const params = useParams();
  const uniqueId = params!.uniqueId as string;

  // State for KYC data to get darpan ID
  const [kycData, setKycData] = useState<KycInformationWithNgo | null>(null);
  const [isLoadingKyc, setIsLoadingKyc] = useState(true);

  // Fetch KYC information to get darpan ID
  useEffect(() => {
    const fetchKycData = async () => {
      try {
        setIsLoadingKyc(true);
        const kycResponse = await fetchKycInformation(uniqueId);
        if (kycResponse && kycResponse.length > 0) {
          const kycRecord = kycResponse[0] as KycInformationWithNgo;
          setKycData(kycRecord);
        }
      } catch (error) {
        console.error("Error fetching KYC data:", error);
        // If we can't fetch KYC data, we'll fall back to DEFAULT_DARPAN_ID
      } finally {
        setIsLoadingKyc(false);
      }
    };

    if (uniqueId) {
      fetchKycData();
    }
  }, [uniqueId]);

  const handleSubmit = async () => {
    if (!recordedVideoBlob) {
      setSubmitError("No video recorded. Please record a video first.");
      return;
    }

    // Get the darpan ID from KYC data or fall back to default
    const darpanId = kycData?.ngoInfo?.darpan_id || DEFAULT_DARPAN_ID;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const result = await uploadVideoToAPI(recordedVideoBlob, darpanId);

      // Clear the video after successful upload
      clearRecordedVideo();
      router.push("/success");
    } catch (error) {
      console.error("Upload error:", error);
      setSubmitError(
        error instanceof Error
          ? error.message
          : "Failed to upload video. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRetake = () => {
    clearRecordedVideo(); // Clear the recorded video before retaking
    router.push(`/recording/${uniqueId}`);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
        <DorightLogo />

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
            {t("review.title", "Do It Right—Double Check!")}
          </h1>

          <p className="text-gray-600">
            {t(
              "review.description",
              "Check your video—ensure great lighting and that it meets all verification requirements before submitting."
            )}
          </p>

          <VideoPlayer />
        </div>

        <div className="space-y-4">
          {/* Error message */}
          {submitError && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{submitError}</p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <Button
              onClick={handleRetake}
              variant="outline"
              className="h-14 text-lg font-medium rounded-lg border-orange-400 text-orange-400 hover:bg-orange-50"
              disabled={isSubmitting}
            >
              {t("review.retake", "Retake")}
            </Button>

            <Button
              onClick={handleSubmit}
              className="h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting || !recordedVideoBlob || isLoadingKyc}
            >
              {isLoadingKyc
                ? t("review.loading", "Loading...")
                : isSubmitting
                ? t("review.uploading", "Uploading...")
                : t("review.submit", "Submit")}
            </Button>
          </div>

          <p className="text-sm text-gray-600 text-center">
            {!recordedVideoBlob
              ? "Please record a video first"
              : isLoadingKyc
              ? "Loading verification details..."
              : t(
                  "review.help",
                  "Tap Play to check your KYC video and confirm it's all good."
                )}
          </p>
        </div>
      </div>
    </div>
  );
}
