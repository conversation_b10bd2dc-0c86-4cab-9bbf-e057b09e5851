"use client";

import { useRef, useState, useEffect } from "react";
import { Box, Button } from "@mui/material";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import PauseIcon from "@mui/icons-material/Pause";
import { useVideo } from "../contexts/VideoContext";

function VideoPlayer() {
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const { videoURL } = useVideo();

  // Set the video source when videoURL is available
  useEffect(() => {
    if (videoRef.current && videoURL) {
      videoRef.current.src = videoURL;
    }
  }, [videoURL]);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  return (
    <Box
      sx={{
        position: "relative",
        width: "100%",
        aspectRatio: "16/9",
        bgcolor: "grey.100",
        borderRadius: 2,
        overflow: "hidden",
      }}
    >
      <video
        ref={videoRef}
        style={{ width: "100%", height: "100%", objectFit: "cover" }}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        controls={false}
      >
        {!videoURL && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              height: "100%",
              color: "#666",
              fontSize: "14px",
            }}
          >
            No video recorded yet
          </div>
        )}
      </video>

      {/* Play/Pause overlay - only show if video is available */}
      {videoURL && (
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Button
            onClick={togglePlay}
            sx={{
              width: 64,
              height: 64,
              borderRadius: "50%",
              bgcolor: "rgba(255, 255, 255, 0.2)",
              backdropFilter: "blur(4px)",
              border: "2px solid rgba(255, 255, 255, 0.5)",
              "&:hover": {
                bgcolor: "rgba(255, 255, 255, 0.3)",
              },
            }}
          >
            {isPlaying ? (
              <PauseIcon sx={{ fontSize: 32, color: "white" }} />
            ) : (
              <PlayArrowIcon sx={{ fontSize: 32, color: "white", ml: 1 }} />
            )}
          </Button>
        </Box>
      )}

      {/* Show message when no video is available */}
      {!videoURL && (
        <Box
          sx={{
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "text.secondary",
            fontSize: "14px",
          }}
        >
          No video recorded yet
        </Box>
      )}

      {/* Progress bar */}
      <Box
        sx={{
          position: "absolute",
          bottom: 16,
          left: 16,
          right: 16,
        }}
      >
        <Box
          sx={{
            width: "100%",
            height: 4,
            bgcolor: "rgba(255, 255, 255, 0.3)",
            borderRadius: 2,
          }}
        >
          <Box
            sx={{
              width: "33%",
              height: "100%",
              bgcolor: "white",
              borderRadius: 2,
            }}
          />
        </Box>
      </Box>
    </Box>
  );
}

export default VideoPlayer;
