"use client"

import { createContext, useContext, useState } from "react"

const TranslationContext = createContext()

const translations = {
  en: {
    "requirements.title": "Get Set to doright — Here's What You Need:",
    "requirements.subtitle": "Make sure you have:",
    "requirements.internet": "A stable internet connection",
    "requirements.helper": "Someone to hold the phone for you while recording",
    "requirements.certificate": "Your NGO's registration certificate",
    "requirements.access": "Access to your NGO office or operational site",
    "requirements.representative":
      "An authorized representative (with their name, designation, and mobile number and their government ID)",
    "requirements.important": "Important:",
    "requirements.location": "You must be within 100 meters of your registered NGO address.",
    "requirements.duration": "Keep your video under 2–3 minutes.",
    "requirements.continue": "Let's Do This",
    "requirements.agreement": "By continuing, you agree to be video verified — safely stored, just for doright.",
    // Add more translations as needed
  },
  hi: {
    "requirements.title": "doright के लिए तैयार हो जाइए — यहाँ है आपको क्या चाहिए:",
    "requirements.subtitle": "सुनिश्चित करें कि आपके पास है:",
    "requirements.internet": "एक स्थिर इंटरनेट कनेक्शन",
    "requirements.helper": "रिकॉर्डिंग के दौरान फोन पकड़ने के लिए कोई व्यक्ति",
    "requirements.certificate": "आपके NGO का पंजीकरण प्रमाणपत्र",
    "requirements.access": "आपके NGO कार्यालय या परिचालन स्थल तक पहुंच",
    "requirements.representative": "एक अधिकृत प्रतिनिधि (उनके नाम, पदनाम, और मोबाइल नंबर और उनकी सरकारी ID के साथ)",
    "requirements.important": "महत्वपूर्ण:",
    "requirements.location": "आपको अपने पंजीकृत NGO पते के 100 मीटर के भीतर होना चाहिए।",
    "requirements.duration": "अपना वीडियो 2-3 मिनट से कम रखें।",
    "requirements.continue": "चलिए करते हैं",
    "requirements.agreement": "जारी रखकर, आप वीडियो सत्यापन के लिए सहमत हैं — सुरक्षित रूप से संग्रहीत, केवल doright के लिए।",
    // Add more translations as needed
  },
  mr: {
    "requirements.title": "doright साठी तयार व्हा — तुम्हाला काय हवे ते येथे आहे:",
    "requirements.subtitle": "खात्री करा की तुमच्याकडे आहे:",
    "requirements.internet": "एक स्थिर इंटरनेट कनेक्शन",
    "requirements.helper": "रेकॉर्डिंग दरम्यान फोन धरण्यासाठी कोणीतरी",
    "requirements.certificate": "तुमच्या NGO चे नोंदणी प्रमाणपत्र",
    "requirements.access": "तुमच्या NGO कार्यालय किंवा कार्यक्षेत्रात प्रवेश",
    "requirements.representative": "एक अधिकृत प्रतिनिधी (त्यांचे नाव, पदनाम, आणि मोबाइल नंबर आणि त्यांचा सरकारी ID)",
    "requirements.important": "महत्वाचे:",
    "requirements.location": "तुम्ही तुमच्या नोंदणीकृत NGO पत्त्याच्या 100 मीटरच्या आत असले पाहिजे.",
    "requirements.duration": "तुमचा व्हिडिओ 2-3 मिनिटांपेक्षा कमी ठेवा.",
    "requirements.continue": "चला हे करूया",
    "requirements.agreement": "पुढे जाऊन, तुम्ही व्हिडिओ सत्यापनासाठी सहमत आहात — सुरक्षितपणे संग्रहित, फक्त doright साठी.",
    // Add more translations as needed
  },
}

export function TranslationProvider({ children }) {
  const [language, setLanguage] = useState("en")

  const t = (key, fallback) => {
    return translations[language]?.[key] || fallback
  }

  const value = {
    language,
    setLanguage,
    t,
  }

  return <TranslationContext.Provider value={value}>{children}</TranslationContext.Provider>
}

export function useTranslation() {
  const context = useContext(TranslationContext)
  if (context === undefined) {
    throw new Error("useTranslation must be used within a TranslationProvider")
  }
  return context
}
