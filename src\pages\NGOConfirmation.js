"use client"
import { useNavigate } from "react-router-dom"
import { Box, Button, Container, Typography, Divider } from "@mui/material"
import { useTranslation } from "../hooks/useTranslation"
import DorightLogo from "../components/DorightLogo"

function NGOConfirmation() {
  const navigate = useNavigate()
  const { t } = useTranslation()

  return (
    <Container maxWidth="sm" sx={{ minHeight: "100vh", display: "flex", flexDirection: "column", py: 4 }}>
      <Box sx={{ width: "100%", maxWidth: 360, mx: "auto", display: "flex", flexDirection: "column", gap: 3, flex: 1 }}>
        <DorightLogo />

        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600, color: "text.primary" }}>
            {t("ngo.title", "Confirm and Empower Your NGO.")}
          </Typography>

          <Box sx={{ mt: 4, display: "flex", flexDirection: "column", gap: 3 }}>
            <Box>
              <Typography variant="h6" sx={{ mb: 1, color: "text.secondary" }}>
                {t("ngo.name", "NGO Name:")}
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                Wirelessmind Foundation
              </Typography>
            </Box>

            <Divider />

            <Box>
              <Typography variant="h6" sx={{ mb: 1, color: "text.secondary" }}>
                {t("ngo.darpan", "Darpan ID:")}
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                KA/2018/0186525
              </Typography>
            </Box>

            <Divider />

            <Box>
              <Typography variant="h6" sx={{ mb: 1, color: "text.secondary" }}>
                {t("ngo.address", "NGO Address:")}
              </Typography>
              <Typography variant="body1" fontWeight={500}>
                203, Brahma Suncity, Wadgaon Sheri, Pune, Maharashtra, India, 411014
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          <Button
            onClick={() => navigate("/instructions")}
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            sx={{ py: 1.5, fontSize: "1.1rem" }}
          >
            {t("ngo.proceed", "Proceed")}
          </Button>

          <Typography variant="body2" align="center" sx={{ mt: 2, color: "text.secondary" }}>
            {t("ngo.error", "Spotted an error? Email <NAME_EMAIL> and we'll sort it out.")}
          </Typography>
        </Box>
      </Box>
    </Container>
  )
}

export default NGOConfirmation
