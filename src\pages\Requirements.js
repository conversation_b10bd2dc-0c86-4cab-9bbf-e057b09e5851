"use client";
import { useNavigate } from "react-router-dom";
import { Box, Button, Container, Typography, Paper } from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";
import DorightLogo from "../components/DorightLogo";

function Requirements() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleContinue = () => {
    
    navigate("/ngo-confirmation");
  };

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        py: 4,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 3,
          flex: 1,
        }}
      >
        <DorightLogo />

        <Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            {t(
              "requirements.title",
              "Get Set to doright — Here's What You Need:"
            )}
          </Typography>

          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, color: "text.primary" }}>
              {t("requirements.subtitle", "Make sure you have:")}
            </Typography>

            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  1.
                </Typography>
                <Typography variant="body1">
                  {t("requirements.internet", "A stable internet connection")}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  2.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.helper",
                    "Someone to hold the phone for you while recording"
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  3.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.certificate",
                    "Your NGO's registration certificate"
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  4.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.access",
                    "Access to your NGO office or operational site"
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  5.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.representative",
                    "An authorized representative (with their name, designation, and mobile number and their government ID)"
                  )}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Paper
            elevation={0}
            sx={{ bgcolor: "grey.50", p: 2, mt: 3, borderRadius: 2 }}
          >
            <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
              {t("requirements.important", "Important:")}
            </Typography>
            <Box component="ul" sx={{ pl: 2, m: 0 }}>
              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                {t(
                  "requirements.location",
                  "You must be within 100 meters of your registered NGO address."
                )}
              </Typography>
              <Typography component="li" variant="body2">
                {t(
                  "requirements.duration",
                  "Keep your video under 2–3 minutes."
                )}
              </Typography>
            </Box>
          </Paper>
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          <Button
            onClick={handleContinue}
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            sx={{ py: 1.5, fontSize: "1.1rem" }}
          >
            {t("requirements.continue", "Let's Do ")}
          </Button>

          <Typography
            variant="body2"
            align="center"
            sx={{ mt: 2, color: "text.secondary" }}
          >
            {t(
              "requirements.agreement",
              "By continuing, you agree to be video verified — safely stored, just for doright."
            )}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default Requirements;
