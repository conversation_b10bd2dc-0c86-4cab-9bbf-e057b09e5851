"use client";

import { createContext, useContext, useState } from "react";

const VideoContext = createContext();

export function VideoProvider({ children }) {
  const [recordedVideoBlob, setRecordedVideoBlob] = useState(null);
  const [videoURL, setVideoURL] = useState(null);

  const saveRecordedVideo = (blob) => {
    setRecordedVideoBlob(blob);
    // Create a URL for the blob that can be used in video elements
    if (videoURL) {
      URL.revokeObjectURL(videoURL); // Clean up previous URL
    }
    const newVideoURL = URL.createObjectURL(blob);
    setVideoURL(newVideoURL);
  };

  const clearRecordedVideo = () => {
    if (videoURL) {
      URL.revokeObjectURL(videoURL);
    }
    setRecordedVideoBlob(null);
    setVideoURL(null);
  };

  const value = {
    recordedVideoBlob,
    videoURL,
    saveRecordedVideo,
    clearRecordedVideo,
  };

  return (
    <VideoContext.Provider value={value}>
      {children}
    </VideoContext.Provider>
  );
}

export function useVideo() {
  const context = useContext(VideoContext);
  if (context === undefined) {
    throw new Error("useVideo must be used within a VideoProvider");
  }
  return context;
}
