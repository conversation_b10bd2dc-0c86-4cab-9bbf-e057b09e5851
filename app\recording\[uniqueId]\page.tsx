"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useTranslation } from "../../hooks/use-translation";
import { DorightLogo } from "../../components/doright-logo";
import { VideoRecorder } from "../../components/video-recorder";

// Step configuration with auto-advance thresholds
const TOTAL_RECORDING_TIME = 150; // 2.5 minutes total
const STEPS = [
  {
    id: 1,
    title: "Let's Begin at the Front — Entrance & Signage",
    description:
      "1. Begin with your NGO's entrance. Make sure the nameplate or signage is clear, then gently pan the camera to show the area around.",
    autoAdvanceAt: 30, // Auto-advance after 30 seconds
  },
  {
    id: 2,
    title: "Show Where the Impact Happens.",
    description:
      "2. Give us a quick tour of your office, meeting rooms, or workstations — the spaces where your team makes change real.",
    autoAdvanceAt: 60, // Auto-advance after 1 minute total
  },
  {
    id: 3,
    title: "Show Off Your Impact Tools.",
    description:
      "3. Display any posters, banners, documents, or items that reflect your NGO's work and purpose. Let your mission speak visually.",
    autoAdvanceAt: 90, // Auto-advance after 1.5 minutes total
  },
  {
    id: 4,
    title: "Show Your Team in Action",
    description:
      "4. Introduce your team members or show them at work. If possible, briefly show any ongoing projects, beneficiaries being served, or activities that demonstrate your NGO's daily operations.",
    autoAdvanceAt: 120, // Auto-advance after 2 minutes total
  },
  {
    id: 5,
    title: "Let's wrap this up the right way!",
    description:
      '5. Your authorized representative holds their Government ID, says: "Hi, I\'m [Full Name], [Designation], mobile number [XXX]. I confirm I represent [NGO Name] and am completing this verification."',
    autoAdvanceAt: 150, // Auto-advance after 2.5 minutes total (end)
  },
];

export default function UnifiedRecording() {
  const router = useRouter();
  const { t } = useTranslation();
  const params = useParams();
  const uniqueId = params!.uniqueId as string;

  // Main state
  const [currentStep, setCurrentStep] = useState(1);
  const [isRecording, setIsRecording] = useState(false);
  const [timeLeft, setTimeLeft] = useState(TOTAL_RECORDING_TIME);
  const [totalRecordingTime, setTotalRecordingTime] = useState(0);

  const currentStepData = STEPS[currentStep - 1];

  // Timer for countdown and auto-advance
  useEffect(() => {
    if (isRecording && timeLeft > 0) {
      const timer = setTimeout(() => {
        const newTimeLeft = timeLeft - 1;
        const newTotalTime = totalRecordingTime + 1;

        setTimeLeft(newTimeLeft);
        setTotalRecordingTime(newTotalTime);

        // Check if we should auto-advance to next step (only if we haven't manually advanced past this point)
        const currentStepData = STEPS[currentStep - 1];
        if (
          newTotalTime >= currentStepData.autoAdvanceAt &&
          currentStep < STEPS.length &&
          newTotalTime === currentStepData.autoAdvanceAt // Only auto-advance exactly at the threshold
        ) {
          setCurrentStep(currentStep + 1);
        } else if (newTimeLeft === 0) {
          // Recording complete, go to review
          setIsRecording(false);
          router.push("/review");
        }
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [isRecording, timeLeft, totalRecordingTime, currentStep]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}.${secs.toString().padStart(2, "0")} mins left`;
  };

  const formatTotalTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleRecordingToggle = (recording: boolean) => {
    setIsRecording(recording);
  };

  const handleNextStep = () => {
    if (currentStep < STEPS.length) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);

      // When manually advancing, jump the timer to the CURRENT step's threshold (not next step's)
      const currentStepData = STEPS[currentStep - 1]; // Current step before advancing
      const timeToAdvanceTo =
        TOTAL_RECORDING_TIME - currentStepData.autoAdvanceAt;
      setTimeLeft(timeToAdvanceTo);
      setTotalRecordingTime(currentStepData.autoAdvanceAt);
    } else {
      // All steps completed, go to review
      setIsRecording(false);
      router.push(`/review/${uniqueId}`);
    }
  };

  const handleFinish = () => {
    if (currentStep < STEPS.length) {
      handleNextStep();
    } else {
      setIsRecording(false);
      router.push(`/review/${uniqueId}`);
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto flex flex-col h-full">
        {/* Header - Fixed height */}
        <div className="flex justify-between items-center mb-6">
          <DorightLogo />
          <span className="text-sm text-gray-500">
            Step {currentStep} of {STEPS.length}
          </span>
        </div>

        {/* Instructions Section - Fixed height container */}
        <div className="mb-6">
          {/* Fixed height container for title to prevent layout shifts */}
          <div className="h-16 mb-4 flex items-start">
            <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
              {t(`step${currentStep}.title`, currentStepData.title)}
            </h1>
          </div>

          {/* Fixed height container for description to prevent layout shifts */}
          <div className="h-24 overflow-y-auto">
            <p className="text-gray-600 text-sm leading-relaxed">
              {t(`step${currentStep}.description`, currentStepData.description)}
            </p>
          </div>
        </div>

        {/* Video Section - Fixed position */}
        <div className="mb-6">
          <VideoRecorder
            isRecording={isRecording}
            onRecordingChange={handleRecordingToggle}
          />

          <div className="flex justify-between items-center text-sm text-gray-600 mt-4">
            <span>{isRecording ? "Recording..." : "Ready to record"}</span>
            <span>{formatTime(timeLeft)}</span>
          </div>
        </div>

        {/* Controls Section - Fixed at bottom */}
        <div className="mt-auto space-y-4">
          {currentStep === 1 && !isRecording ? (
            <Button
              onClick={() => setIsRecording(true)}
              className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            >
              {t("recording.start", "Start Recording")}
            </Button>
          ) : (
            <Button
              onClick={handleFinish}
              className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
            >
              {currentStep === STEPS.length
                ? t("recording.complete", "Complete Recording")
                : t("recording.next", "Next Step")}
            </Button>
          )}

          {/* Fixed height container for help text to prevent button movement */}
          <div className="h-12 flex items-center justify-center">
            <p className="text-sm text-gray-600 text-center">
              {isRecording
                ? t(
                    "recording.autoAdvance",
                    "Recording will automatically advance to next step when timer reaches zero"
                  )
                : currentStep === 1
                ? t(
                    "recording.help",
                    "Ready to go? Tap Start Recording to begin."
                  )
                : t(
                    "recording.continue",
                    "Tap the play button on video to continue recording or Next Step to proceed."
                  )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
