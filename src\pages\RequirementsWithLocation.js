import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";
import <PERSON>right<PERSON><PERSON> from "../components/DorightLogo";
import {
  fetchKycInformation,
  fetchNGOData,
  getCurrentLocation,
  verifyLocationProximity,
} from "../utils/locationUtils";

function RequirementsWithLocation() {
  const { uniqueId } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [locationState, setLocationState] = useState({
    status: "loading", // loading, requesting_permission, verifying, success, error, too_far
    error: null,
    kycData: null,
    ngoData: null,
    userLocation: null,
    distance: null,
    distanceInMeters: null,
  });

  useEffect(() => {
    if (uniqueId) {
      initializeLocationVerification();
    }
  }, [uniqueId]);

  const initializeLocationVerification = async () => {
    try {
      setLocationState((prev) => ({ ...prev, status: "loading" }));

      // Fetch KYC information
      const kycData = await fetchKycInformation(uniqueId);

      // Fetch NGO data
      const ngoData = await fetchNGOData(kycData.ngo_id);

      setLocationState((prev) => ({
        ...prev,
        kycData,
        ngoData,
        status: "requesting_permission",
      }));

      // Request location permission and verify
      await verifyUserLocation(ngoData);
    } catch (error) {
      console.error("Error initializing location verification:", error);
      setLocationState((prev) => ({
        ...prev,
        status: "error",
        error: error.message,
      }));
    }
  };

  const verifyUserLocation = async (ngoData) => {
    try {
      setLocationState((prev) => ({ ...prev, status: "verifying" }));

      // Get user's current location
      const userLocation = await getCurrentLocation();

      // Verify proximity to NGO location
      const ngoLocation = {
        latitude: ngoData.latitude,
        longitude: ngoData.longitude,
      };

      console.log(userLocation);
      console.log(ngoLocation);

      const proximityResult = verifyLocationProximity(
        userLocation,
        ngoLocation
      );

      setLocationState((prev) => ({
        ...prev,
        userLocation,
        distance: proximityResult.distance,
        distanceInMeters: proximityResult.distanceInMeters,
        status: proximityResult.isWithinRange ? "success" : "too_far",
      }));
    } catch (error) {
      console.error("Error verifying location:", error);
      setLocationState((prev) => ({
        ...prev,
        status: "error",
        error: error.message,
      }));
    }
  };

  const handleContinue = () => {
    if (locationState.status === "success") {
      navigate("/ngo-confirmation", {
        state: {
          uniqueId,
          kycData: locationState.kycData,
          ngoData: locationState.ngoData,
        },
      });
    }
  };

  const handleRetryLocation = () => {
    clg("Retry location check");
    if (locationState.ngoData) {
      verifyUserLocation(locationState.ngoData);
    }
  };

  const renderLocationStatus = () => {
    switch (locationState.status) {
      case "loading":
        return (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <CircularProgress size={40} />
            <Typography variant="body1" sx={{ mt: 2 }}>
              Loading verification details...
            </Typography>
          </Box>
        );

      case "requesting_permission":
        return (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <CircularProgress size={40} />
            <Typography variant="body1" sx={{ mt: 2 }}>
              Requesting location permission...
            </Typography>
          </Box>
        );

      case "verifying":
        return (
          <Box sx={{ textAlign: "center", py: 4 }}>
            <CircularProgress size={40} />
            <Typography variant="body1" sx={{ mt: 2 }}>
              Verifying your location...
            </Typography>
          </Box>
        );

      case "success":
        return (
          <Alert severity="success" sx={{ mb: 3 }}>
            <Typography variant="body2">
              ✓ Location verified! You are within{" "}
              {Math.round(locationState.distanceInMeters)} meters of your NGO
              location.
            </Typography>
          </Alert>
        );

      case "too_far":
        return (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              You are too far from your NGO location. You are currently{" "}
              {Math.round(locationState.distanceInMeters)} meters away.
            </Typography>
            <Typography variant="body2">
              Please move within 100 meters of your registered NGO address to
              continue.
            </Typography>
            <Button onClick={handleRetryLocation} size="small" sx={{ mt: 1 }}>
              Retry Location Check
            </Button>
          </Alert>
        );

      case "error":
        return (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Error: {locationState.error}
            </Typography>
            <Button onClick={handleRetryLocation} size="small" sx={{ mt: 1 }}>
              Retry
            </Button>
          </Alert>
        );

      default:
        return null;
    }
  };

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        py: 4,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 3,
          flex: 1,
        }}
      >
        <DorightLogo />

        {/* Location Status */}
        {renderLocationStatus()}

        {/* NGO Information */}
        {locationState.ngoData && (
          <Paper
            elevation={0}
            sx={{ bgcolor: "grey.50", p: 2, borderRadius: 2 }}
          >
            <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
              NGO Information:
            </Typography>
            <Typography variant="body2">
              <strong>Name:</strong> {locationState.ngoData.name}
            </Typography>
            <Typography variant="body2">
              <strong>Address:</strong> {locationState.ngoData.address}
            </Typography>
          </Paper>
        )}

        <Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            {t(
              "requirements.title",
              "Get Set to doright — Here's What You Need:"
            )}
          </Typography>

          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" sx={{ mb: 2, color: "text.primary" }}>
              {t("requirements.subtitle", "Make sure you have:")}
            </Typography>

            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  1.
                </Typography>
                <Typography variant="body1">
                  {t("requirements.internet", "A stable internet connection")}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  2.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.helper",
                    "Someone to hold the phone for you while recording"
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  3.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.certificate",
                    "Your NGO's registration certificate"
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  4.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.access",
                    "Access to your NGO office or operational site"
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  5.
                </Typography>
                <Typography variant="body1">
                  {t(
                    "requirements.representative",
                    "An authorized representative (with their name, designation, and mobile number and their government ID)"
                  )}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Paper
            elevation={0}
            sx={{ bgcolor: "grey.50", p: 2, mt: 3, borderRadius: 2 }}
          >
            <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
              {t("requirements.important", "Important:")}
            </Typography>
            <Box component="ul" sx={{ pl: 2, m: 0 }}>
              <Typography component="li" variant="body2" sx={{ mb: 0.5 }}>
                {t(
                  "requirements.location",
                  "You must be within 100 meters of your registered NGO address."
                )}
              </Typography>
              <Typography component="li" variant="body2">
                {t(
                  "requirements.duration",
                  "Keep your video under 2–3 minutes."
                )}
              </Typography>
            </Box>
          </Paper>
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          <Button
            onClick={handleContinue}
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            disabled={locationState.status !== "success"}
            sx={{ py: 1.5, fontSize: "1.1rem" }}
          >
            {t("requirements.continue", "Let's Do This")}
          </Button>

          <Typography
            variant="body2"
            align="center"
            sx={{ mt: 2, color: "text.secondary" }}
          >
            {t(
              "requirements.agreement",
              "By continuing, you agree to be video verified — safely stored, just for doright."
            )}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default RequirementsWithLocation;
