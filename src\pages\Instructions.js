"use client";
import { useNavigate } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>, Container, Typography } from "@mui/material";
import { useTranslation } from "../hooks/useTranslation";
import DorightLogo from "../components/DorightLogo";

function Instructions() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Container
      maxWidth="sm"
      sx={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        py: 4,
      }}
    >
      <Box
        sx={{
          width: "100%",
          maxWidth: 360,
          mx: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 3,
          flex: 1,
        }}
      >
        <DorightLogo />

        <Box>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{ fontWeight: 600, color: "text.primary" }}
          >
            {t("instructions.title", "Capture These to Act for Impact.")}
          </Typography>

          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, color: "text.primary" }}>
              {t(
                "instructions.subtitle",
                "Please record these key elements clearly:"
              )}
            </Typography>

            <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  1.
                </Typography>
                <Box>
                  <Typography variant="body1" fontWeight={600} component="span">
                    {t("instructions.entrance", "Entrance & Signage:")}
                  </Typography>
                  <Typography variant="body1" component="span" sx={{ ml: 1 }}>
                    {t(
                      "instructions.entranceDesc",
                      "Show your nameplate and move slowly to capture surroundings."
                    )}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  2.
                </Typography>
                <Box>
                  <Typography variant="body1" fontWeight={600} component="span">
                    {t("instructions.workspace", "Workspace:")}
                  </Typography>
                  <Typography variant="body1" component="span" sx={{ ml: 1 }}>
                    {t(
                      "instructions.workspaceDesc",
                      "Film your office, meeting rooms, activity rooms or workstations."
                    )}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  3.
                </Typography>
                <Box>
                  <Typography variant="body1" fontWeight={600} component="span">
                    {t("instructions.branded", "Branded Items:")}
                  </Typography>
                  <Typography variant="body1" component="span" sx={{ ml: 1 }}>
                    {t(
                      "instructions.brandedDesc",
                      "Display posters, banners or official documents."
                    )}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  4.
                </Typography>
                <Box>
                  <Typography variant="body1" fontWeight={600} component="span">
                    {t("instructions.certificate", "Certificate:")}
                  </Typography>
                  <Typography variant="body1" component="span" sx={{ ml: 1 }}>
                    {t(
                      "instructions.certificateDesc",
                      "Hold up your registration certificate ensure the name is visible and tilt side-to-side."
                    )}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: "flex", gap: 2 }}>
                <Typography variant="body1" fontWeight={500}>
                  5.
                </Typography>
                <Box>
                  <Typography variant="body1" fontWeight={600} component="span">
                    {t(
                      "instructions.representative",
                      "Representative & ID Check:"
                    )}
                  </Typography>
                  <Typography variant="body1" component="span" sx={{ ml: 1 }}>
                    {t(
                      "instructions.representativeDesc",
                      "Authorized person holds their government ID next to their face, states their"
                    )}
                  </Typography>
                  <Typography variant="body1" fontWeight={600} component="span">
                    {" "}
                    name, mobile, and designation,
                  </Typography>
                  <Typography variant="body1" component="span" sx={{ ml: 1 }}>
                    {t(
                      "instructions.script",
                      'and says: "Hi, I\'m [Full Name], [Designation], mobile number [XXX]. I confirm, I represent [NGO Name] and am completing this verification."'
                    )}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>

        <Box sx={{ mt: "auto", pt: 4 }}>
          <Button
            onClick={() => navigate("/recording")}
            variant="contained"
            color="primary"
            fullWidth
            size="large"
            sx={{ py: 1.5, fontSize: "1.1rem" }}
          >
            {t("instructions.start", "Go DoRight")}
          </Button>
        </Box>
      </Box>
    </Container>
  );
}

export default Instructions;
