/**
 * Utility functions for location verification
 */

/**
 * Fetch KYC information from the backend
 * @param {string} uniqueId - The unique ID for the KYC request
 * @returns {Promise<Object>} KYC information
 */
export async function fetchKycInformation(uniqueId) {
  try {
    const response = await fetch(`http://localhost:4000/api/kyc/${uniqueId}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching KYC information:", error);
    throw error;
  }
}

/**
 * Fetch NGO data from the backend
 * @param {number} ngoId - The NGO ID
 * @returns {Promise<Object>} NGO data
 */
export async function fetchNGOData(ngoId) {
  try {
    const response = await fetch(`http://localhost:4000/api/ngo/${ngoId}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching NGO data:", error);
    throw error;
  }
}

/**
 * Get current location using browser's geolocation API
 * @returns {Promise<Object>} Location coordinates {latitude, longitude}
 */
export function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error("Geolocation is not supported by this browser."));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coordinates = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        };

        // Log coordinates for debugging
        console.log("🌍 Current Location Coordinates:", coordinates);
        console.log(`📍 Latitude: ${coordinates.latitude}`);
        console.log(`📍 Longitude: ${coordinates.longitude}`);
        console.log(
          `🔗 Google Maps Link: https://www.google.com/maps?q=${coordinates.latitude},${coordinates.longitude}`
        );

        resolve(coordinates);
      },
      (error) => {
        let errorMessage = "Unknown error occurred";
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "User denied the request for Geolocation.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable.";
            break;
          case error.TIMEOUT:
            errorMessage = "The request to get user location timed out.";
            break;
        }
        reject(new Error(errorMessage));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  });
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {Object} coord1 - First coordinate {latitude, longitude}
 * @param {Object} coord2 - Second coordinate {latitude, longitude}
 * @returns {number} Distance in kilometers
 */
export function calculateDistance(coord1, coord2) {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = deg2rad(coord2.latitude - coord1.latitude);
  const dLon = deg2rad(coord2.longitude - coord1.longitude);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(coord1.latitude)) *
      Math.cos(deg2rad(coord2.latitude)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in kilometers
  return distance;
}

/**
 * Convert degrees to radians
 * @param {number} deg - Degrees
 * @returns {number} Radians
 */
function deg2rad(deg) {
  return deg * (Math.PI / 180);
}

/**
 * Verify if user location is within proximity of NGO location
 * @param {Object} userLocation - User's coordinates {latitude, longitude}
 * @param {Object} ngoLocation - NGO's coordinates {latitude, longitude}
 * @param {number} maxDistanceKm - Maximum allowed distance in kilometers (default: 0.1 = 100 meters)
 * @returns {Object} Verification result {isWithinRange, distance, distanceInMeters}
 */
export function verifyLocationProximity(
  userLocation,
  ngoLocation,
  maxDistanceKm = 0.5
) {
  const distance = calculateDistance(userLocation, ngoLocation);
  const distanceInMeters = distance * 1000;
  const isWithinRange = distance <= maxDistanceKm;

  // Log location verification details
  console.log("📍 Location Verification Details:");
  console.log("👤 User Location:", userLocation);
  console.log("🏢 NGO Location:", ngoLocation);
  console.log(
    `📏 Distance: ${distance.toFixed(3)} km (${Math.round(
      distanceInMeters
    )} meters)`
  );
  console.log(
    `✅ Within Range (${maxDistanceKm * 1000}m): ${
      isWithinRange ? "YES" : "NO"
    }`
  );

  return {
    isWithinRange,
    distance,
    distanceInMeters,
  };
}
