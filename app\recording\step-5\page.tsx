"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useTranslation } from "../../hooks/use-translation"
import { DorightLogo } from "../../components/doright-logo"
import { VideoRecorder } from "../../components/video-recorder"

export default function RecordingStep5() {
  const router = useRouter()
  const { t } = useTranslation()
  const [isRecording, setIsRecording] = useState(true) // Start as true since we're continuing from previous step
  const [timeLeft, setTimeLeft] = useState(30) // 30 seconds
  const [autoAdvanceTimer, setAutoAdvanceTimer] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (isRecording && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [isRecording, timeLeft])

  useEffect(() => {
    if (isRecording) {
      // Set a timer to automatically advance to the next step after 30 seconds
      const timer = setTimeout(() => {
        router.push("/review")
      }, 30000)

      setAutoAdvanceTimer(timer)

      return () => {
        if (autoAdvanceTimer) clearTimeout(autoAdvanceTimer)
      }
    }
  }, [isRecording, router])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}.${secs.toString().padStart(2, "0")} mins left`
  }

  const handleFinish = () => {
    // Don't stop recording, just navigate to next step
    router.push("/review")
  }

  return (
    <div className="min-h-screen bg-white flex flex-col p-6">
      <div className="w-full max-w-sm mx-auto space-y-6 flex-1">
        <div className="flex justify-between items-center">
          <DorightLogo />
          <span className="text-sm text-gray-500">Step 5 of 5</span>
        </div>

        <div className="space-y-6">
          <h1 className="text-2xl font-semibold text-gray-800 leading-tight">
            {t("step5.title", "Let's wrap this up the right way!")}
          </h1>

          <div className="text-gray-600 space-y-2">
            <p className="font-medium">
              {t("step5.subtitle", "5. Your authorized representative holds their Government ID, says:")}
            </p>
            <p className="text-sm bg-gray-50 p-3 rounded-lg">
              {t(
                "step5.script",
                '"Hi, I\'m [Full Name], [Designation], mobile number [XXX]. I confirm I represent [NGO Name] and am completing this verification."',
              )}
            </p>
          </div>

          <VideoRecorder isRecording={isRecording} onRecordingChange={setIsRecording} />

          <div className="flex justify-between items-center text-sm text-gray-600">
            <span>{isRecording ? "Recording...." : "Ready to record"}</span>
            <span>{formatTime(timeLeft)}</span>
          </div>
        </div>

        <div className="space-y-4">
          <Button
            onClick={handleFinish}
            className="w-full h-14 bg-orange-400 hover:bg-orange-500 text-white text-lg font-medium rounded-lg"
          >
            {t("step5.finish", "Finish")}
          </Button>

          <p className="text-sm text-gray-600 text-center">
            {t("step5.help", "Want to retry? Tap Finish now. Otherwise, wait 30 s to automatically move to next step.")}
          </p>
        </div>
      </div>
    </div>
  )
}
